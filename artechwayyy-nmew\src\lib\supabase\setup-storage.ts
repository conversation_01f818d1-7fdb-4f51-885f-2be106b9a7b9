import { createClient } from './client';

export async function setupStorageBucket() {
  const supabase = createClient();
  
  try {
    console.log('Setting up storage bucket...');
    
    // Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('Error listing buckets:', listError);
      return { success: false, error: listError.message };
    }

    const existingBucket = buckets?.find(bucket => bucket.id === 'post_images');
    
    if (existingBucket) {
      console.log('post_images bucket already exists:', existingBucket);
      return { success: true, message: 'Bucket already exists' };
    }

    // Create the bucket
    const { data, error } = await supabase.storage.createBucket('post_images', {
      public: true,
      fileSizeLimit: 5242880, // 5MB
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
    });

    if (error) {
      console.error('Error creating bucket:', error);
      return { success: false, error: error.message };
    }

    console.log('Bucket created successfully:', data);
    return { success: true, message: 'Bucket created successfully', data };
    
  } catch (error: any) {
    console.error('Setup error:', error);
    return { success: false, error: error.message };
  }
}

export async function checkBucketPermissions() {
  const supabase = createClient();
  
  try {
    // Test upload permissions
    const testFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const testPath = `test-${Date.now()}.txt`;
    
    const { error: uploadError } = await supabase.storage
      .from('post_images')
      .upload(testPath, testFile);
    
    if (uploadError) {
      return { success: false, error: `Upload test failed: ${uploadError.message}` };
    }

    // Clean up test file
    await supabase.storage.from('post_images').remove([testPath]);
    
    return { success: true, message: 'Bucket permissions are working correctly' };
    
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}
