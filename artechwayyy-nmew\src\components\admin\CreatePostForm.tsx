"use client";

import { useEffect, useState } from "react";
import { useF<PERSON>, type <PERSON>mit<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { FilePlus, Sparkles, UploadCloud, X, Bug, Settings } from "lucide-react";
import { generateBlogPost } from "@/ai/flows/generate-blog-post";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { testStorageConnection, testImageUpload } from "@/lib/supabase/storage-test";
import { setupStorageBucket, checkBucketPermissions } from "@/lib/supabase/setup-storage";

type FormValues = {
  title: string;
  slug: string;
  content: string;
  tags: string;
  category: string;
  featuredImage: FileList;
};

const generateSlug = (title: string) => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, "")
    .replace(/\s+/g, "-")
    .replace(/-+/g, "-");
};

export function CreatePostForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isAiGenerating, setIsAiGenerating] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      title: "",
      slug: "",
      content: "",
      tags: "",
      category: "",
    },
  });

  const titleValue = watch("title");
  const featuredImageValue = watch("featuredImage");

  useEffect(() => {
    const slug = generateSlug(titleValue);
    setValue("slug", slug);
  }, [titleValue, setValue]);

  // Handle image preview
  useEffect(() => {
    if (featuredImageValue && featuredImageValue.length > 0) {
      const file = featuredImageValue[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
    }
  }, [featuredImageValue]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file (PNG, JPG, GIF, WebP, or SVG).",
          variant: "destructive",
        });
        e.target.value = ''; // Clear the input
        return;
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        e.target.value = ''; // Clear the input
        return;
      }

      // Update form value manually to ensure it's captured
      setValue("featuredImage", files);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file (PNG, JPG, GIF, WebP, or SVG).",
          variant: "destructive",
        });
        return;
      }

      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      // Create a new FileList-like object
      const dt = new DataTransfer();
      dt.items.add(file);
      const fileList = dt.files;

      // Update the form value
      setValue("featuredImage", fileList);
    }
  };

  const removeImage = () => {
    setValue("featuredImage", new DataTransfer().files);
    setImagePreview(null);
  };

  const handleDebugStorage = async () => {
    console.log("Testing storage connection...");
    const result = await testStorageConnection();
    console.log("Storage test result:", result);

    toast({
      title: result.success ? "Storage Test Passed" : "Storage Test Failed",
      description: result.success
        ? `Authenticated: ${result.data?.authenticated}, Buckets: ${result.data?.buckets}, Post Images Bucket: ${result.data?.postImagesBucket}`
        : result.error || "Unknown error",
      variant: result.success ? "default" : "destructive",
    });
  };

  const handleSetupBucket = async () => {
    console.log("Setting up storage bucket...");
    const result = await setupStorageBucket();
    console.log("Setup result:", result);

    toast({
      title: result.success ? "Bucket Setup Complete" : "Bucket Setup Failed",
      description: result.success
        ? result.message || "Storage bucket is ready"
        : result.error || "Unknown error",
      variant: result.success ? "default" : "destructive",
    });

    // Test permissions after setup
    if (result.success) {
      const permResult = await checkBucketPermissions();
      console.log("Permission test:", permResult);
    }
  };

  const handleGenerateWithAi = async () => {
    if (!titleValue) {
      toast({
        title: "Title is required",
        description: "Please enter a title to generate content with AI.",
        variant: "destructive",
      });
      return;
    }

    setIsAiGenerating(true);
    try {
      const result = await generateBlogPost({ title: titleValue });
      if (result) {
        setValue("content", result.content);
        setValue("tags", result.tags);
        toast({
          title: "AI Content Generated!",
          description: "The blog content and tags have been filled in for you.",
        });
      }
    } catch (error) {
      console.error("AI Generation Error:", error);
      toast({
        title: "AI Generation Failed",
        description:
          "There was an error generating content. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAiGenerating(false);
    }
  };

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsLoading(true);

    try {
      const imageFile = data.featuredImage[0];
      if (!imageFile) {
        throw new Error("Featured image is required.");
      }

      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error("You must be logged in to upload images.");
      }

      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(imageFile.type)) {
        throw new Error("Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.");
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      if (imageFile.size > maxSize) {
        throw new Error("File size too large. Please upload an image smaller than 5MB.");
      }

      // 1. Upload image to Supabase Storage with unique filename
      const timestamp = Date.now();
      const fileExtension = imageFile.name.split('.').pop();
      const fileName = `${data.slug}-${timestamp}.${fileExtension}`;
      const filePath = fileName; // Remove 'public/' prefix as bucket is already public

      console.log("Uploading file:", fileName, "Size:", imageFile.size, "Type:", imageFile.type);

      const { error: uploadError } = await supabase.storage
        .from("post_images")
        .upload(filePath, imageFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error("Image upload error:", uploadError);
        throw new Error(`Image upload failed: ${uploadError.message}`);
      }

      // 2. Get public URL of the uploaded image
      const { data: publicUrlData } = supabase.storage
        .from("post_images")
        .getPublicUrl(filePath);

      if (!publicUrlData?.publicUrl) {
        throw new Error("Could not get public URL for the image.");
      }
      const imageUrl = publicUrlData.publicUrl;

      // 3. Insert post data into the 'posts' table
      console.log("Inserting post data with image URL:", imageUrl);

      const { error: insertError } = await supabase.from("posts").insert([
        {
          title: data.title,
          slug: data.slug,
          content: data.content,
          tags: data.tags,
          category: data.category,
          image_url: imageUrl,
          status: 'published',
          created_at: new Date().toISOString(),
          // You might want to add author_id if you have user management
        },
      ]);

      if (insertError) {
        console.error("Database insert error:", insertError);
        // If database insert fails, try to clean up the uploaded image
        try {
          await supabase.storage.from("post_images").remove([filePath]);
        } catch (cleanupError) {
          console.error("Failed to cleanup uploaded image:", cleanupError);
        }
        throw new Error(`Failed to save post: ${insertError.message}`);
      }

      toast({
        title: "Post Published!",
        description: "Your new blog post has been successfully published.",
      });

      reset();
      setImagePreview(null);
      router.push("/admin/posts");

    } catch (error: any) {
      console.error("Form submission error:", error);

      // Provide more specific error messages
      let errorMessage = "An unexpected error occurred. Please try again.";

      if (error.message.includes("Invalid file type")) {
        errorMessage = error.message;
      } else if (error.message.includes("File size too large")) {
        errorMessage = error.message;
      } else if (error.message.includes("logged in")) {
        errorMessage = "Please log in to upload images.";
      } else if (error.message.includes("Image upload failed")) {
        errorMessage = "Failed to upload image. Please check your internet connection and try again.";
      } else if (error.message.includes("Failed to save post")) {
        errorMessage = "Image uploaded but failed to save post. Please try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Submission Failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="flex flex-col">
      <CardHeader>
        <div className="flex items-center gap-2">
          <FilePlus className="h-6 w-6 text-primary" />
          <CardTitle className="text-xl font-headline">
            Create a New Post
          </CardTitle>
        </div>
        <CardDescription>
          Fill out the form below or use AI to generate a post.
        </CardDescription>
      </CardHeader>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col flex-grow"
      >
        <CardContent className="space-y-4 flex-grow">
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              placeholder="Your Post Title"
              {...register("title", { required: "Title is required." })}
              disabled={isLoading || isAiGenerating}
            />
            {errors.title && (
              <p className="text-sm text-destructive">{errors.title.message}</p>
            )}
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleSetupBucket}
              disabled={isLoading || isAiGenerating}
            >
              <Settings className="mr-2 h-4 w-4" />
              Setup Bucket
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleDebugStorage}
              disabled={isLoading || isAiGenerating}
            >
              <Bug className="mr-2 h-4 w-4" />
              Test Storage
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={handleGenerateWithAi}
              disabled={isAiGenerating || !titleValue}
            >
              <Sparkles className="mr-2 h-4 w-4" />
              {isAiGenerating ? "Generating..." : "Generate with AI"}
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor="slug">Slug</Label>
            <Input
              id="slug"
              placeholder="your-post-slug"
              {...register("slug", { required: "Slug is required." })}
              readOnly
              className="bg-muted/50"
            />
            {errors.slug && (
              <p className="text-sm text-destructive">{errors.slug.message}</p>
            )}
             <p className="text-xs text-muted-foreground">URL-friendly version of the title.</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              placeholder="Write your amazing blog post here, or generate it with AI."
              className="min-h-[300px]"
              {...register("content", { required: "Content is required." })}
              disabled={isLoading || isAiGenerating}
            />
            {errors.content && (
              <p className="text-sm text-destructive">
                {errors.content.message}
              </p>
            )}
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                placeholder="e.g., AI, Art, Tech"
                {...register("tags")}
                disabled={isLoading || isAiGenerating}
              />
              <p className="text-xs text-muted-foreground">
                Comma-separated tags.
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Controller
                name="category"
                control={control}
                rules={{ required: "Category is required." }}
                render={({ field }) => (
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoading || isAiGenerating}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AI News">AI News</SelectItem>
                      <SelectItem value="AI Design">AI Design</SelectItem>
                      <SelectItem value="AI for Business">
                        AI for Business
                      </SelectItem>
                      <SelectItem value="AI Marketing">AI Marketing</SelectItem>
                      <SelectItem value="Future of AI">Future of AI</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
               {errors.category && (
              <p className="text-sm text-destructive">{errors.category.message}</p>
            )}
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="featuredImage">Featured Image</Label>
            <div className="flex items-center justify-center w-full">
              {imagePreview ? (
                <div className="relative w-full">
                  <div className="relative w-full h-48 border-2 border-dashed rounded-lg overflow-hidden">
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      fill
                      className="object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={removeImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2 text-center">
                    Click the X to remove or click below to change image
                  </p>
                  <label
                    htmlFor="featuredImage"
                    className="inline-flex items-center justify-center px-4 py-2 mt-2 text-sm font-medium text-primary border border-primary rounded-md cursor-pointer hover:bg-primary/10 transition-colors"
                  >
                    Change Image
                  </label>
                </div>
              ) : (
                <label
                  htmlFor="featuredImage"
                  className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${
                    isDragOver
                      ? "border-primary bg-primary/10"
                      : "border-muted-foreground/25 bg-muted/50 hover:bg-muted/75"
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <UploadCloud className="w-8 h-8 mb-2 text-muted-foreground" />
                    <p className="mb-2 text-sm text-muted-foreground">
                      <span className="font-semibold">Click to upload</span> or
                      drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, GIF, WebP or SVG (max 5MB)
                    </p>
                  </div>
                </label>
              )}
              <Input
                id="featuredImage"
                type="file"
                accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"
                className="hidden"
                {...register("featuredImage", { required: "Featured image is required."})}
                onChange={handleImageChange}
                disabled={isLoading || isAiGenerating}
              />
            </div>
             {errors.featuredImage && (
              <p className="text-sm text-destructive">{errors.featuredImage.message}</p>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            type="submit"
            disabled={isLoading || isAiGenerating}
            size="lg"
          >
            {isLoading ? "Publishing..." : "Publish Post"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}

    