{"version": 3, "sources": [], "sections": [{"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\r\nimport {googleAI} from '@genkit-ai/googleai';\r\n\r\nexport const ai = genkit({\r\n  plugins: [googleAI()],\r\n  model: 'googleai/gemini-2.5-flash',\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/ai/flows/generate-blog-post.ts"], "sourcesContent": ["'use server';\r\n/**\r\n * @fileOverview AI-powered SEO blog post generator.\r\n *\r\n * - generateBlogPost - A function that generates a blog post from a title.\r\n * - GenerateBlogPostInput - The input type for the generateBlogPost function.\r\n * - GenerateBlogPostOutput - The return type for the generateBlogPost function.\r\n */\r\n\r\nimport {ai} from '@/ai/genkit';\r\nimport {z} from 'genkit';\r\n\r\nconst GenerateBlogPostInputSchema = z.object({\r\n  title: z\r\n    .string()\r\n    .describe('The title of the blog post to be generated.'),\r\n});\r\nexport type GenerateBlogPostInput = z.infer<typeof GenerateBlogPostInputSchema>;\r\n\r\nconst GenerateBlogPostOutputSchema = z.object({\r\n  content: z.string().describe('The generated SEO-optimized blog post content, formatted in markdown.'),\r\n  tags: z.string().describe('A comma-separated list of relevant SEO keywords/tags for the blog post.'),\r\n});\r\nexport type GenerateBlogPostOutput = z.infer<typeof GenerateBlogPostOutputSchema>;\r\n\r\nexport async function generateBlogPost(input: GenerateBlogPostInput): Promise<GenerateBlogPostOutput> {\r\n  return generateBlogPostFlow(input);\r\n}\r\n\r\nconst prompt = ai.definePrompt({\r\n  name: 'generateBlogPostPrompt',\r\n  input: {schema: GenerateBlogPostInputSchema},\r\n  output: {schema: GenerateBlogPostOutputSchema},\r\n  prompt: `You are an expert SEO copywriter and content strategist. Your task is to write a high-quality, engaging, and SEO-optimized blog post based on the provided title.\r\n\r\nThe blog post should be well-structured, easy to read, and provide real value to the reader. Use markdown for formatting (e.g., headings, lists, bold text). Do not use any HTML tags.\r\n\r\nAlso, provide a comma-separated list of relevant tags for the blog post to improve its searchability.\r\n\r\nBlog Post Title: {{{title}}}`,\r\n});\r\n\r\nconst generateBlogPostFlow = ai.defineFlow(\r\n  {\r\n    name: 'generateBlogPostFlow',\r\n    inputSchema: GenerateBlogPostInputSchema,\r\n    outputSchema: GenerateBlogPostOutputSchema,\r\n  },\r\n  async input => {\r\n    const {output} = await prompt(input);\r\n    return output!;\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;AACA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,8BAA8B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,uIAAA,CAAA,IAAC,CACL,MAAM,GACN,QAAQ,CAAC;AACd;AAGA,MAAM,+BAA+B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,SAAS,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGO,eAAe,iBAAiB,KAA4B;IACjE,OAAO,qBAAqB;AAC9B;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAA2B;IAC3C,QAAQ;QAAC,QAAQ;IAA4B;IAC7C,QAAQ,CAAC;;;;;;4BAMiB,CAAC;AAC7B;AAEA,MAAM,uBAAuB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAM;IACJ,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;IAC9B,OAAO;AACT;;;IA1BoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/.next-internal/server/app/admin/posts/create/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {generateBlogPost as '40d6d3ae52e39459a461b92189ae613e2eae71f4da'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/app/admin/posts/create/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/posts/create/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/posts/create/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/app/admin/posts/create/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/admin/posts/create/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/admin/posts/create/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}