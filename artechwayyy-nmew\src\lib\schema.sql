-- Create the posts table
CREATE TABLE IF NOT EXISTS posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  content TEXT,
  tags TEXT,
  category TEXT,
  image_url TEXT,
  status TEXT DEFAULT 'published',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add status column if it doesn't exist (for existing tables)
ALTER TABLE posts ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'published';

-- Enable Row Level Security for the posts table
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;

-- Create policies for the posts table
-- Allow authenticated users to do everything
CREATE POLICY "Allow full access for authenticated users"
ON posts FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);


-- Create a bucket for post images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('post_images', 'post_images', TRUE, 5242880, ARRA<PERSON>['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])
ON CONFLICT (id) DO UPDATE SET
  public = TRUE,
  file_size_limit = 5242880,
  allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

-- Create policies for the post_images bucket
-- Public read access
CREATE POLICY "Allow public read access on post images" 
ON storage.objects FOR SELECT
USING ( bucket_id = 'post_images' );

-- Authenticated users can insert
CREATE POLICY "Allow authenticated insert on post images" 
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK ( bucket_id = 'post_images' );

-- Authenticated users can update
CREATE POLICY "Allow authenticated update on post images"
ON storage.objects FOR UPDATE
TO authenticated
USING ( bucket_id = 'post_images' );

-- Authenticated users can delete
CREATE POLICY "Allow authenticated delete on post images"
ON storage.objects FOR DELETE
TO authenticated
USING ( bucket_id = 'post_images' );
