{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/admin/AdminSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FileText, PlusCircle } from \"lucide-react\";\r\n\r\nconst menuItems = [\r\n  { href: \"/admin/posts\", label: \"Manage Posts\", icon: FileText },\r\n  { href: \"/admin/posts/create\", label: \"Create Post\", icon: PlusCircle },\r\n];\r\n\r\nexport function AdminSidebar() {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <aside className=\"w-64 bg-background border-r p-4 hidden md:block\">\r\n      <nav className=\"space-y-2\">\r\n        {menuItems.map((item) => (\r\n          <Button\r\n            key={item.href}\r\n            variant={pathname === item.href ? \"secondary\" : \"ghost\"}\r\n            className=\"w-full justify-start\"\r\n            asChild\r\n          >\r\n            <Link href={item.href}>\r\n              <item.icon className=\"mr-2 h-4 w-4\" />\r\n              {item.label}\r\n            </Link>\r\n          </Button>\r\n        ))}\r\n      </nav>\r\n    </aside>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAAA;;;AANA;;;;;AAQA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAgB,OAAO;QAAgB,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAuB,OAAO;QAAe,MAAM,qNAAA,CAAA,aAAU;IAAC;CACvE;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAM,WAAU;kBACf,cAAA,6LAAC;YAAI,WAAU;sBACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,SAAM;oBAEL,SAAS,aAAa,KAAK,IAAI,GAAG,cAAc;oBAChD,WAAU;oBACV,OAAO;8BAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,KAAK,IAAI;;0CACnB,6LAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,KAAK;;;;;;;mBAPR,KAAK,IAAI;;;;;;;;;;;;;;;AAc1B;GAtBgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Select = SelectPrimitive.Root\r\n\r\nconst SelectGroup = SelectPrimitive.Group\r\n\r\nconst SelectValue = SelectPrimitive.Value\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n))\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n))\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n))\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n))\r\nSelectContent.displayName = SelectPrimitive.Content.displayName\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n))\r\nSelectItem.displayName = SelectPrimitive.Item.displayName\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,qKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,qKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,qKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/ai/flows/generate-blog-post.ts"], "sourcesContent": ["'use server';\r\n/**\r\n * @fileOverview AI-powered SEO blog post generator.\r\n *\r\n * - generateBlogPost - A function that generates a blog post from a title.\r\n * - GenerateBlogPostInput - The input type for the generateBlogPost function.\r\n * - GenerateBlogPostOutput - The return type for the generateBlogPost function.\r\n */\r\n\r\nimport {ai} from '@/ai/genkit';\r\nimport {z} from 'genkit';\r\n\r\nconst GenerateBlogPostInputSchema = z.object({\r\n  title: z\r\n    .string()\r\n    .describe('The title of the blog post to be generated.'),\r\n});\r\nexport type GenerateBlogPostInput = z.infer<typeof GenerateBlogPostInputSchema>;\r\n\r\nconst GenerateBlogPostOutputSchema = z.object({\r\n  content: z.string().describe('The generated SEO-optimized blog post content, formatted in markdown.'),\r\n  tags: z.string().describe('A comma-separated list of relevant SEO keywords/tags for the blog post.'),\r\n});\r\nexport type GenerateBlogPostOutput = z.infer<typeof GenerateBlogPostOutputSchema>;\r\n\r\nexport async function generateBlogPost(input: GenerateBlogPostInput): Promise<GenerateBlogPostOutput> {\r\n  return generateBlogPostFlow(input);\r\n}\r\n\r\nconst prompt = ai.definePrompt({\r\n  name: 'generateBlogPostPrompt',\r\n  input: {schema: GenerateBlogPostInputSchema},\r\n  output: {schema: GenerateBlogPostOutputSchema},\r\n  prompt: `You are an expert SEO copywriter and content strategist. Your task is to write a high-quality, engaging, and SEO-optimized blog post based on the provided title.\r\n\r\nThe blog post should be well-structured, easy to read, and provide real value to the reader. Use markdown for formatting (e.g., headings, lists, bold text). Do not use any HTML tags.\r\n\r\nAlso, provide a comma-separated list of relevant tags for the blog post to improve its searchability.\r\n\r\nBlog Post Title: {{{title}}}`,\r\n});\r\n\r\nconst generateBlogPostFlow = ai.defineFlow(\r\n  {\r\n    name: 'generateBlogPostFlow',\r\n    inputSchema: GenerateBlogPostInputSchema,\r\n    outputSchema: GenerateBlogPostOutputSchema,\r\n  },\r\n  async input => {\r\n    const {output} = await prompt(input);\r\n    return output!;\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;;;;IAyBsB,mBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\r\n\r\nexport function createClient() {\r\n  return createBrowserClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD;AAI3B", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/lib/supabase/storage-test.ts"], "sourcesContent": ["import { createClient } from './client';\n\nexport async function testStorageConnection() {\n  const supabase = createClient();\n  \n  try {\n    // Test 1: Check if user is authenticated\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    console.log('Session check:', { session: !!session, error: sessionError });\n    \n    if (!session) {\n      return { success: false, error: 'Not authenticated' };\n    }\n\n    // Test 2: List buckets to verify storage access\n    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();\n    console.log('Buckets:', { buckets, error: bucketsError });\n\n    // Test 3: Check if post_images bucket exists\n    const postImagesBucket = buckets?.find(bucket => bucket.id === 'post_images');\n    console.log('Post images bucket:', postImagesBucket);\n\n    // Test 4: Try to list files in the bucket\n    const { data: files, error: filesError } = await supabase.storage\n      .from('post_images')\n      .list('', { limit: 5 });\n    console.log('Files in bucket:', { files, error: filesError });\n\n    return { \n      success: true, \n      data: { \n        authenticated: !!session,\n        buckets: buckets?.length || 0,\n        postImagesBucket: !!postImagesBucket,\n        canListFiles: !filesError\n      }\n    };\n  } catch (error) {\n    console.error('Storage test error:', error);\n    return { success: false, error: error.message };\n  }\n}\n\nexport async function testImageUpload(file: File) {\n  const supabase = createClient();\n  \n  try {\n    const timestamp = Date.now();\n    const fileExtension = file.name.split('.').pop();\n    const fileName = `test-${timestamp}.${fileExtension}`;\n    \n    console.log('Testing upload:', { fileName, size: file.size, type: file.type });\n\n    const { data, error } = await supabase.storage\n      .from('post_images')\n      .upload(fileName, file, {\n        cacheControl: '3600',\n        upsert: false\n      });\n\n    if (error) {\n      console.error('Upload test failed:', error);\n      return { success: false, error: error.message };\n    }\n\n    console.log('Upload test successful:', data);\n\n    // Get public URL\n    const { data: urlData } = supabase.storage\n      .from('post_images')\n      .getPublicUrl(fileName);\n\n    console.log('Public URL:', urlData?.publicUrl);\n\n    // Clean up test file\n    await supabase.storage.from('post_images').remove([fileName]);\n\n    return { \n      success: true, \n      data: { \n        path: data.path,\n        publicUrl: urlData?.publicUrl\n      }\n    };\n  } catch (error) {\n    console.error('Upload test error:', error);\n    return { success: false, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,yCAAyC;QACzC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QACjF,QAAQ,GAAG,CAAC,kBAAkB;YAAE,SAAS,CAAC,CAAC;YAAS,OAAO;QAAa;QAExE,IAAI,CAAC,SAAS;YACZ,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoB;QACtD;QAEA,gDAAgD;QAChD,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,WAAW;QACjF,QAAQ,GAAG,CAAC,YAAY;YAAE;YAAS,OAAO;QAAa;QAEvD,6CAA6C;QAC7C,MAAM,mBAAmB,SAAS,KAAK,CAAA,SAAU,OAAO,EAAE,KAAK;QAC/D,QAAQ,GAAG,CAAC,uBAAuB;QAEnC,0CAA0C;QAC1C,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAAS,OAAO,CAC9D,IAAI,CAAC,eACL,IAAI,CAAC,IAAI;YAAE,OAAO;QAAE;QACvB,QAAQ,GAAG,CAAC,oBAAoB;YAAE;YAAO,OAAO;QAAW;QAE3D,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,eAAe,CAAC,CAAC;gBACjB,SAAS,SAAS,UAAU;gBAC5B,kBAAkB,CAAC,CAAC;gBACpB,cAAc,CAAC;YACjB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAEO,eAAe,gBAAgB,IAAU;IAC9C,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,gBAAgB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;QAC9C,MAAM,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,eAAe;QAErD,QAAQ,GAAG,CAAC,mBAAmB;YAAE;YAAU,MAAM,KAAK,IAAI;YAAE,MAAM,KAAK,IAAI;QAAC;QAE5E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAC3C,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,MAAM;YACtB,cAAc;YACd,QAAQ;QACV;QAEF,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QAEA,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,iBAAiB;QACjB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,SAAS,OAAO,CACvC,IAAI,CAAC,eACL,YAAY,CAAC;QAEhB,QAAQ,GAAG,CAAC,eAAe,SAAS;QAEpC,qBAAqB;QACrB,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC;YAAC;SAAS;QAE5D,OAAO;YACL,SAAS;YACT,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,WAAW,SAAS;YACtB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/lib/supabase/setup-storage.ts"], "sourcesContent": ["import { createClient } from './client';\n\nexport async function setupStorageBucket() {\n  const supabase = createClient();\n  \n  try {\n    console.log('Setting up storage bucket...');\n    \n    // Check if bucket already exists\n    const { data: buckets, error: listError } = await supabase.storage.listBuckets();\n    \n    if (listError) {\n      console.error('Error listing buckets:', listError);\n      return { success: false, error: listError.message };\n    }\n\n    const existingBucket = buckets?.find(bucket => bucket.id === 'post_images');\n    \n    if (existingBucket) {\n      console.log('post_images bucket already exists:', existingBucket);\n      return { success: true, message: 'Bucket already exists' };\n    }\n\n    // Create the bucket\n    const { data, error } = await supabase.storage.createBucket('post_images', {\n      public: true,\n      fileSizeLimit: 5242880, // 5MB\n      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']\n    });\n\n    if (error) {\n      console.error('Error creating bucket:', error);\n      return { success: false, error: error.message };\n    }\n\n    console.log('Bucket created successfully:', data);\n    return { success: true, message: 'Bucket created successfully', data };\n    \n  } catch (error: any) {\n    console.error('Setup error:', error);\n    return { success: false, error: error.message };\n  }\n}\n\nexport async function checkBucketPermissions() {\n  const supabase = createClient();\n  \n  try {\n    // Test upload permissions\n    const testFile = new File(['test'], 'test.txt', { type: 'text/plain' });\n    const testPath = `test-${Date.now()}.txt`;\n    \n    const { error: uploadError } = await supabase.storage\n      .from('post_images')\n      .upload(testPath, testFile);\n    \n    if (uploadError) {\n      return { success: false, error: `Upload test failed: ${uploadError.message}` };\n    }\n\n    // Clean up test file\n    await supabase.storage.from('post_images').remove([testPath]);\n    \n    return { success: true, message: 'Bucket permissions are working correctly' };\n    \n  } catch (error: any) {\n    return { success: false, error: error.message };\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,iCAAiC;QACjC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,WAAW;QAE9E,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBAAE,SAAS;gBAAO,OAAO,UAAU,OAAO;YAAC;QACpD;QAEA,MAAM,iBAAiB,SAAS,KAAK,CAAA,SAAU,OAAO,EAAE,KAAK;QAE7D,IAAI,gBAAgB;YAClB,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAwB;QAC3D;QAEA,oBAAoB;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,YAAY,CAAC,eAAe;YACzE,QAAQ;YACR,eAAe;YACf,kBAAkB;gBAAC;gBAAc;gBAAa;gBAAa;gBAAc;aAAgB;QAC3F;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;QAEA,QAAQ,GAAG,CAAC,gCAAgC;QAC5C,OAAO;YAAE,SAAS;YAAM,SAAS;YAA+B;QAAK;IAEvE,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF;AAEO,eAAe;IACpB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,0BAA0B;QAC1B,MAAM,WAAW,IAAI,KAAK;YAAC;SAAO,EAAE,YAAY;YAAE,MAAM;QAAa;QACrE,MAAM,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;QAEzC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,eACL,MAAM,CAAC,UAAU;QAEpB,IAAI,aAAa;YACf,OAAO;gBAAE,SAAS;gBAAO,OAAO,CAAC,oBAAoB,EAAE,YAAY,OAAO,EAAE;YAAC;QAC/E;QAEA,qBAAqB;QACrB,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC;YAAC;SAAS;QAE5D,OAAO;YAAE,SAAS;YAAM,SAAS;QAA2C;IAE9E,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,SAAS;YAAO,OAAO,MAAM,OAAO;QAAC;IAChD;AACF", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/admin/CreatePostForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useF<PERSON>, type <PERSON>mit<PERSON><PERSON><PERSON>, Controller } from \"react-hook-form\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  Card<PERSON>ooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { FilePlus, Sparkles, UploadCloud, X, Bug, Settings } from \"lucide-react\";\r\nimport { generateBlogPost } from \"@/ai/flows/generate-blog-post\";\r\nimport { createClient } from \"@/lib/supabase/client\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport { testStorageConnection, testImageUpload } from \"@/lib/supabase/storage-test\";\r\nimport { setupStorageBucket, checkBucketPermissions } from \"@/lib/supabase/setup-storage\";\r\n\r\ntype FormValues = {\r\n  title: string;\r\n  slug: string;\r\n  content: string;\r\n  tags: string;\r\n  category: string;\r\n  featuredImage: FileList;\r\n};\r\n\r\nconst generateSlug = (title: string) => {\r\n  return title\r\n    .toLowerCase()\r\n    .trim()\r\n    .replace(/[^a-z0-9\\s-]/g, \"\")\r\n    .replace(/\\s+/g, \"-\")\r\n    .replace(/-+/g, \"-\");\r\n};\r\n\r\nexport function CreatePostForm() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isAiGenerating, setIsAiGenerating] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState<string | null>(null);\r\n  const [isDragOver, setIsDragOver] = useState(false);\r\n  const { toast } = useToast();\r\n  const supabase = createClient();\r\n  const router = useRouter();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    control,\r\n    watch,\r\n    setValue,\r\n    reset,\r\n    formState: { errors },\r\n  } = useForm<FormValues>({\r\n    defaultValues: {\r\n      title: \"\",\r\n      slug: \"\",\r\n      content: \"\",\r\n      tags: \"\",\r\n      category: \"\",\r\n    },\r\n  });\r\n\r\n  const titleValue = watch(\"title\");\r\n  const featuredImageValue = watch(\"featuredImage\");\r\n\r\n  useEffect(() => {\r\n    const slug = generateSlug(titleValue);\r\n    setValue(\"slug\", slug);\r\n  }, [titleValue, setValue]);\r\n\r\n  // Handle image preview\r\n  useEffect(() => {\r\n    if (featuredImageValue && featuredImageValue.length > 0) {\r\n      const file = featuredImageValue[0];\r\n      const reader = new FileReader();\r\n      reader.onload = (e) => {\r\n        setImagePreview(e.target?.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    } else {\r\n      setImagePreview(null);\r\n    }\r\n  }, [featuredImageValue]);\r\n\r\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n    if (files && files.length > 0) {\r\n      const file = files[0];\r\n\r\n      console.log(\"File selected:\", { name: file.name, size: file.size, type: file.type });\r\n\r\n      // Validate file type\r\n      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];\r\n      if (!allowedTypes.includes(file.type)) {\r\n        console.error(\"Invalid file type:\", file.type);\r\n        toast({\r\n          title: \"Invalid file type\",\r\n          description: \"Please select an image file (PNG, JPG, GIF, WebP, or SVG).\",\r\n          variant: \"destructive\",\r\n        });\r\n        e.target.value = ''; // Clear the input\r\n        return;\r\n      }\r\n\r\n      // Validate file size (5MB limit)\r\n      const maxSize = 5 * 1024 * 1024;\r\n      if (file.size > maxSize) {\r\n        console.error(\"File too large:\", file.size);\r\n        toast({\r\n          title: \"File too large\",\r\n          description: \"Please select an image smaller than 5MB.\",\r\n          variant: \"destructive\",\r\n        });\r\n        e.target.value = ''; // Clear the input\r\n        return;\r\n      }\r\n\r\n      // Update form value manually to ensure it's captured\r\n      setValue(\"featuredImage\", files);\r\n      console.log(\"File set in form:\", files.length);\r\n    } else {\r\n      console.log(\"No files selected\");\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n  };\r\n\r\n  const handleDrop = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setIsDragOver(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files && files.length > 0) {\r\n      const file = files[0];\r\n\r\n      // Validate file type\r\n      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];\r\n      if (!allowedTypes.includes(file.type)) {\r\n        toast({\r\n          title: \"Invalid file type\",\r\n          description: \"Please select an image file (PNG, JPG, GIF, WebP, or SVG).\",\r\n          variant: \"destructive\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Validate file size (5MB limit)\r\n      const maxSize = 5 * 1024 * 1024;\r\n      if (file.size > maxSize) {\r\n        toast({\r\n          title: \"File too large\",\r\n          description: \"Please select an image smaller than 5MB.\",\r\n          variant: \"destructive\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      // Create a new FileList-like object\r\n      const dt = new DataTransfer();\r\n      dt.items.add(file);\r\n      const fileList = dt.files;\r\n\r\n      // Update the form value\r\n      setValue(\"featuredImage\", fileList);\r\n    }\r\n  };\r\n\r\n  const removeImage = () => {\r\n    setValue(\"featuredImage\", new DataTransfer().files);\r\n    setImagePreview(null);\r\n  };\r\n\r\n  const handleDebugStorage = async () => {\r\n    console.log(\"Testing storage connection...\");\r\n    const result = await testStorageConnection();\r\n    console.log(\"Storage test result:\", result);\r\n\r\n    toast({\r\n      title: result.success ? \"Storage Test Passed\" : \"Storage Test Failed\",\r\n      description: result.success\r\n        ? `Authenticated: ${result.data?.authenticated}, Buckets: ${result.data?.buckets}, Post Images Bucket: ${result.data?.postImagesBucket}`\r\n        : result.error || \"Unknown error\",\r\n      variant: result.success ? \"default\" : \"destructive\",\r\n    });\r\n  };\r\n\r\n  const handleSetupBucket = async () => {\r\n    console.log(\"Setting up storage bucket...\");\r\n    const result = await setupStorageBucket();\r\n    console.log(\"Setup result:\", result);\r\n\r\n    toast({\r\n      title: result.success ? \"Bucket Setup Complete\" : \"Bucket Setup Failed\",\r\n      description: result.success\r\n        ? result.message || \"Storage bucket is ready\"\r\n        : result.error || \"Unknown error\",\r\n      variant: result.success ? \"default\" : \"destructive\",\r\n    });\r\n\r\n    // Test permissions after setup\r\n    if (result.success) {\r\n      const permResult = await checkBucketPermissions();\r\n      console.log(\"Permission test:\", permResult);\r\n    }\r\n  };\r\n\r\n  const handleGenerateWithAi = async () => {\r\n    if (!titleValue) {\r\n      toast({\r\n        title: \"Title is required\",\r\n        description: \"Please enter a title to generate content with AI.\",\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsAiGenerating(true);\r\n    try {\r\n      const result = await generateBlogPost({ title: titleValue });\r\n      if (result) {\r\n        setValue(\"content\", result.content);\r\n        setValue(\"tags\", result.tags);\r\n        toast({\r\n          title: \"AI Content Generated!\",\r\n          description: \"The blog content and tags have been filled in for you.\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"AI Generation Error:\", error);\r\n      toast({\r\n        title: \"AI Generation Failed\",\r\n        description:\r\n          \"There was an error generating content. Please try again.\",\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsAiGenerating(false);\r\n    }\r\n  };\r\n\r\n  const onSubmit: SubmitHandler<FormValues> = async (data) => {\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const imageFile = data.featuredImage[0];\r\n      if (!imageFile) {\r\n        throw new Error(\"Featured image is required.\");\r\n      }\r\n\r\n      // Check if user is authenticated\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n      if (!session) {\r\n        throw new Error(\"You must be logged in to upload images.\");\r\n      }\r\n\r\n      // Validate file type and size\r\n      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\r\n      if (!allowedTypes.includes(imageFile.type)) {\r\n        throw new Error(\"Invalid file type. Please upload a JPEG, PNG, GIF, or WebP image.\");\r\n      }\r\n\r\n      const maxSize = 5 * 1024 * 1024; // 5MB\r\n      if (imageFile.size > maxSize) {\r\n        throw new Error(\"File size too large. Please upload an image smaller than 5MB.\");\r\n      }\r\n\r\n      // 1. Upload image to Supabase Storage with unique filename\r\n      const timestamp = Date.now();\r\n      const fileExtension = imageFile.name.split('.').pop();\r\n      const fileName = `${data.slug}-${timestamp}.${fileExtension}`;\r\n      const filePath = fileName; // Remove 'public/' prefix as bucket is already public\r\n\r\n      console.log(\"Uploading file:\", fileName, \"Size:\", imageFile.size, \"Type:\", imageFile.type);\r\n\r\n      const { error: uploadError } = await supabase.storage\r\n        .from(\"post_images\")\r\n        .upload(filePath, imageFile, {\r\n          cacheControl: '3600',\r\n          upsert: false\r\n        });\r\n\r\n      if (uploadError) {\r\n        console.error(\"Image upload error:\", uploadError);\r\n        throw new Error(`Image upload failed: ${uploadError.message}`);\r\n      }\r\n\r\n      // 2. Get public URL of the uploaded image\r\n      const { data: publicUrlData } = supabase.storage\r\n        .from(\"post_images\")\r\n        .getPublicUrl(filePath);\r\n\r\n      if (!publicUrlData?.publicUrl) {\r\n        throw new Error(\"Could not get public URL for the image.\");\r\n      }\r\n      const imageUrl = publicUrlData.publicUrl;\r\n\r\n      // 3. Insert post data into the 'posts' table\r\n      console.log(\"Inserting post data with image URL:\", imageUrl);\r\n\r\n      const { error: insertError } = await supabase.from(\"posts\").insert([\r\n        {\r\n          title: data.title,\r\n          slug: data.slug,\r\n          content: data.content,\r\n          tags: data.tags,\r\n          category: data.category,\r\n          image_url: imageUrl,\r\n          status: 'published',\r\n          created_at: new Date().toISOString(),\r\n          // You might want to add author_id if you have user management\r\n        },\r\n      ]);\r\n\r\n      if (insertError) {\r\n        console.error(\"Database insert error:\", insertError);\r\n        // If database insert fails, try to clean up the uploaded image\r\n        try {\r\n          await supabase.storage.from(\"post_images\").remove([filePath]);\r\n        } catch (cleanupError) {\r\n          console.error(\"Failed to cleanup uploaded image:\", cleanupError);\r\n        }\r\n        throw new Error(`Failed to save post: ${insertError.message}`);\r\n      }\r\n\r\n      toast({\r\n        title: \"Post Published!\",\r\n        description: \"Your new blog post has been successfully published.\",\r\n      });\r\n\r\n      reset();\r\n      setImagePreview(null);\r\n      router.push(\"/admin/posts\");\r\n\r\n    } catch (error: any) {\r\n      console.error(\"Form submission error:\", error);\r\n\r\n      // Provide more specific error messages\r\n      let errorMessage = \"An unexpected error occurred. Please try again.\";\r\n\r\n      if (error.message.includes(\"Invalid file type\")) {\r\n        errorMessage = error.message;\r\n      } else if (error.message.includes(\"File size too large\")) {\r\n        errorMessage = error.message;\r\n      } else if (error.message.includes(\"logged in\")) {\r\n        errorMessage = \"Please log in to upload images.\";\r\n      } else if (error.message.includes(\"Image upload failed\")) {\r\n        errorMessage = \"Failed to upload image. Please check your internet connection and try again.\";\r\n      } else if (error.message.includes(\"Failed to save post\")) {\r\n        errorMessage = \"Image uploaded but failed to save post. Please try again.\";\r\n      } else if (error.message) {\r\n        errorMessage = error.message;\r\n      }\r\n\r\n      toast({\r\n        title: \"Submission Failed\",\r\n        description: errorMessage,\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className=\"flex flex-col\">\r\n      <CardHeader>\r\n        <div className=\"flex items-center gap-2\">\r\n          <FilePlus className=\"h-6 w-6 text-primary\" />\r\n          <CardTitle className=\"text-xl font-headline\">\r\n            Create a New Post\r\n          </CardTitle>\r\n        </div>\r\n        <CardDescription>\r\n          Fill out the form below or use AI to generate a post.\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <form\r\n        onSubmit={handleSubmit(onSubmit)}\r\n        className=\"flex flex-col flex-grow\"\r\n      >\r\n        <CardContent className=\"space-y-4 flex-grow\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"title\">Title</Label>\r\n            <Input\r\n              id=\"title\"\r\n              placeholder=\"Your Post Title\"\r\n              {...register(\"title\", { required: \"Title is required.\" })}\r\n              disabled={isLoading || isAiGenerating}\r\n            />\r\n            {errors.title && (\r\n              <p className=\"text-sm text-destructive\">{errors.title.message}</p>\r\n            )}\r\n          </div>\r\n          <div className=\"flex justify-end gap-2\">\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleSetupBucket}\r\n              disabled={isLoading || isAiGenerating}\r\n            >\r\n              <Settings className=\"mr-2 h-4 w-4\" />\r\n              Setup Bucket\r\n            </Button>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleDebugStorage}\r\n              disabled={isLoading || isAiGenerating}\r\n            >\r\n              <Bug className=\"mr-2 h-4 w-4\" />\r\n              Test Storage\r\n            </Button>\r\n            <Button\r\n              type=\"button\"\r\n              variant=\"outline\"\r\n              onClick={handleGenerateWithAi}\r\n              disabled={isAiGenerating || !titleValue}\r\n            >\r\n              <Sparkles className=\"mr-2 h-4 w-4\" />\r\n              {isAiGenerating ? \"Generating...\" : \"Generate with AI\"}\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"slug\">Slug</Label>\r\n            <Input\r\n              id=\"slug\"\r\n              placeholder=\"your-post-slug\"\r\n              {...register(\"slug\", { required: \"Slug is required.\" })}\r\n              readOnly\r\n              className=\"bg-muted/50\"\r\n            />\r\n            {errors.slug && (\r\n              <p className=\"text-sm text-destructive\">{errors.slug.message}</p>\r\n            )}\r\n             <p className=\"text-xs text-muted-foreground\">URL-friendly version of the title.</p>\r\n          </div>\r\n\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"content\">Content</Label>\r\n            <Textarea\r\n              id=\"content\"\r\n              placeholder=\"Write your amazing blog post here, or generate it with AI.\"\r\n              className=\"min-h-[300px]\"\r\n              {...register(\"content\", { required: \"Content is required.\" })}\r\n              disabled={isLoading || isAiGenerating}\r\n            />\r\n            {errors.content && (\r\n              <p className=\"text-sm text-destructive\">\r\n                {errors.content.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"tags\">Tags</Label>\r\n              <Input\r\n                id=\"tags\"\r\n                placeholder=\"e.g., AI, Art, Tech\"\r\n                {...register(\"tags\")}\r\n                disabled={isLoading || isAiGenerating}\r\n              />\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Comma-separated tags.\r\n              </p>\r\n            </div>\r\n            <div className=\"space-y-2\">\r\n              <Label htmlFor=\"category\">Category</Label>\r\n              <Controller\r\n                name=\"category\"\r\n                control={control}\r\n                rules={{ required: \"Category is required.\" }}\r\n                render={({ field }) => (\r\n                  <Select\r\n                    onValueChange={field.onChange}\r\n                    defaultValue={field.value}\r\n                    disabled={isLoading || isAiGenerating}\r\n                  >\r\n                    <SelectTrigger id=\"category\">\r\n                      <SelectValue placeholder=\"Select a category\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"AI News\">AI News</SelectItem>\r\n                      <SelectItem value=\"AI Design\">AI Design</SelectItem>\r\n                      <SelectItem value=\"AI for Business\">\r\n                        AI for Business\r\n                      </SelectItem>\r\n                      <SelectItem value=\"AI Marketing\">AI Marketing</SelectItem>\r\n                      <SelectItem value=\"Future of AI\">Future of AI</SelectItem>\r\n                    </SelectContent>\r\n                  </Select>\r\n                )}\r\n              />\r\n               {errors.category && (\r\n              <p className=\"text-sm text-destructive\">{errors.category.message}</p>\r\n            )}\r\n            </div>\r\n          </div>\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"featuredImage\">Featured Image</Label>\r\n            <div className=\"flex items-center justify-center w-full\">\r\n              {imagePreview ? (\r\n                <div className=\"relative w-full\">\r\n                  <div className=\"relative w-full h-48 border-2 border-dashed rounded-lg overflow-hidden\">\r\n                    <Image\r\n                      src={imagePreview}\r\n                      alt=\"Preview\"\r\n                      fill\r\n                      className=\"object-cover\"\r\n                    />\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"destructive\"\r\n                      size=\"sm\"\r\n                      className=\"absolute top-2 right-2\"\r\n                      onClick={removeImage}\r\n                    >\r\n                      <X className=\"h-4 w-4\" />\r\n                    </Button>\r\n                  </div>\r\n                  <p className=\"text-sm text-muted-foreground mt-2 text-center\">\r\n                    Click the X to remove or click below to change image\r\n                  </p>\r\n                  <label\r\n                    htmlFor=\"featuredImage\"\r\n                    className=\"inline-flex items-center justify-center px-4 py-2 mt-2 text-sm font-medium text-primary border border-primary rounded-md cursor-pointer hover:bg-primary/10 transition-colors\"\r\n                  >\r\n                    Change Image\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <label\r\n                  htmlFor=\"featuredImage\"\r\n                  className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer transition-colors ${\r\n                    isDragOver\r\n                      ? \"border-primary bg-primary/10\"\r\n                      : \"border-muted-foreground/25 bg-muted/50 hover:bg-muted/75\"\r\n                  }`}\r\n                  onDragOver={handleDragOver}\r\n                  onDragLeave={handleDragLeave}\r\n                  onDrop={handleDrop}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\r\n                    <UploadCloud className=\"w-8 h-8 mb-2 text-muted-foreground\" />\r\n                    <p className=\"mb-2 text-sm text-muted-foreground\">\r\n                      <span className=\"font-semibold\">Click to upload</span> or\r\n                      drag and drop\r\n                    </p>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      PNG, JPG, GIF, WebP or SVG (max 5MB)\r\n                    </p>\r\n                  </div>\r\n                </label>\r\n              )}\r\n              <Input\r\n                id=\"featuredImage\"\r\n                type=\"file\"\r\n                accept=\"image/jpeg,image/png,image/gif,image/webp,image/svg+xml\"\r\n                className=\"hidden\"\r\n                {...register(\"featuredImage\", {\r\n                  required: \"Featured image is required.\",\r\n                  onChange: handleImageChange\r\n                })}\r\n                disabled={isLoading || isAiGenerating}\r\n              />\r\n            </div>\r\n             {errors.featuredImage && (\r\n              <p className=\"text-sm text-destructive\">{errors.featuredImage.message}</p>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n        <CardFooter>\r\n          <Button\r\n            type=\"submit\"\r\n            disabled={isLoading || isAiGenerating}\r\n            size=\"lg\"\r\n          >\r\n            {isLoading ? \"Publishing...\" : \"Publish Post\"}\r\n          </Button>\r\n        </CardFooter>\r\n      </form>\r\n    </Card>\r\n  );\r\n}\r\n\r\n    "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA9BA;;;;;;;;;;;;;;;;;AAyCA,MAAM,eAAe,CAAC;IACpB,OAAO,MACJ,WAAW,GACX,IAAI,GACJ,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAc;QACtB,eAAe;YACb,OAAO;YACP,MAAM;YACN,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;IAEA,MAAM,aAAa,MAAM;IACzB,MAAM,qBAAqB,MAAM;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,OAAO,aAAa;YAC1B,SAAS,QAAQ;QACnB;mCAAG;QAAC;QAAY;KAAS;IAEzB,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,sBAAsB,mBAAmB,MAAM,GAAG,GAAG;gBACvD,MAAM,OAAO,kBAAkB,CAAC,EAAE;gBAClC,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM;gDAAG,CAAC;wBACf,gBAAgB,EAAE,MAAM,EAAE;oBAC5B;;gBACA,OAAO,aAAa,CAAC;YACvB,OAAO;gBACL,gBAAgB;YAClB;QACF;mCAAG;QAAC;KAAmB;IAEvB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,QAAQ,GAAG,CAAC,kBAAkB;gBAAE,MAAM,KAAK,IAAI;gBAAE,MAAM,KAAK,IAAI;gBAAE,MAAM,KAAK,IAAI;YAAC;YAElF,qBAAqB;YACrB,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;gBAAc;aAAgB;YAC5F,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,QAAQ,KAAK,CAAC,sBAAsB,KAAK,IAAI;gBAC7C,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,kBAAkB;gBACvC;YACF;YAEA,iCAAiC;YACjC,MAAM,UAAU,IAAI,OAAO;YAC3B,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,QAAQ,KAAK,CAAC,mBAAmB,KAAK,IAAI;gBAC1C,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,kBAAkB;gBACvC;YACF;YAEA,qDAAqD;YACrD,SAAS,iBAAiB;YAC1B,QAAQ,GAAG,CAAC,qBAAqB,MAAM,MAAM;QAC/C,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,MAAM,OAAO,KAAK,CAAC,EAAE;YAErB,qBAAqB;YACrB,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;gBAAc;aAAgB;YAC5F,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF;YAEA,iCAAiC;YACjC,MAAM,UAAU,IAAI,OAAO;YAC3B,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;gBACA;YACF;YAEA,oCAAoC;YACpC,MAAM,KAAK,IAAI;YACf,GAAG,KAAK,CAAC,GAAG,CAAC;YACb,MAAM,WAAW,GAAG,KAAK;YAEzB,wBAAwB;YACxB,SAAS,iBAAiB;QAC5B;IACF;IAEA,MAAM,cAAc;QAClB,SAAS,iBAAiB,IAAI,eAAe,KAAK;QAClD,gBAAgB;IAClB;IAEA,MAAM,qBAAqB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,CAAA,GAAA,4IAAA,CAAA,wBAAqB,AAAD;QACzC,QAAQ,GAAG,CAAC,wBAAwB;QAEpC,MAAM;YACJ,OAAO,OAAO,OAAO,GAAG,wBAAwB;YAChD,aAAa,OAAO,OAAO,GACvB,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE,cAAc,WAAW,EAAE,OAAO,IAAI,EAAE,QAAQ,sBAAsB,EAAE,OAAO,IAAI,EAAE,kBAAkB,GACtI,OAAO,KAAK,IAAI;YACpB,SAAS,OAAO,OAAO,GAAG,YAAY;QACxC;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD;QACtC,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,MAAM;YACJ,OAAO,OAAO,OAAO,GAAG,0BAA0B;YAClD,aAAa,OAAO,OAAO,GACvB,OAAO,OAAO,IAAI,4BAClB,OAAO,KAAK,IAAI;YACpB,SAAS,OAAO,OAAO,GAAG,YAAY;QACxC;QAEA,+BAA+B;QAC/B,IAAI,OAAO,OAAO,EAAE;YAClB,MAAM,aAAa,MAAM,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;YAC9C,QAAQ,GAAG,CAAC,oBAAoB;QAClC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,YAAY;YACf,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,kBAAkB;QAClB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,6JAAA,CAAA,mBAAgB,AAAD,EAAE;gBAAE,OAAO;YAAW;YAC1D,IAAI,QAAQ;gBACV,SAAS,WAAW,OAAO,OAAO;gBAClC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;gBACJ,OAAO;gBACP,aACE;gBACF,SAAS;YACX;QACF,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,WAAsC,OAAO;QACjD,aAAa;QAEb,IAAI;YACF,MAAM,YAAY,KAAK,aAAa,CAAC,EAAE;YACvC,IAAI,CAAC,WAAW;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,iCAAiC;YACjC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAC5D,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YAEA,8BAA8B;YAC9B,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;aAAa;YAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,GAAG;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;YACvC,IAAI,UAAU,IAAI,GAAG,SAAS;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,2DAA2D;YAC3D,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,gBAAgB,UAAU,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACnD,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,eAAe;YAC7D,MAAM,WAAW,UAAU,sDAAsD;YAEjF,QAAQ,GAAG,CAAC,mBAAmB,UAAU,SAAS,UAAU,IAAI,EAAE,SAAS,UAAU,IAAI;YAEzF,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,eACL,MAAM,CAAC,UAAU,WAAW;gBAC3B,cAAc;gBACd,QAAQ;YACV;YAEF,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY,OAAO,EAAE;YAC/D;YAEA,0CAA0C;YAC1C,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,SAAS,OAAO,CAC7C,IAAI,CAAC,eACL,YAAY,CAAC;YAEhB,IAAI,CAAC,eAAe,WAAW;gBAC7B,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,WAAW,cAAc,SAAS;YAExC,6CAA6C;YAC7C,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;gBACjE;oBACE,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO;oBACrB,MAAM,KAAK,IAAI;oBACf,UAAU,KAAK,QAAQ;oBACvB,WAAW;oBACX,QAAQ;oBACR,YAAY,IAAI,OAAO,WAAW;gBAEpC;aACD;YAED,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,+DAA+D;gBAC/D,IAAI;oBACF,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC;wBAAC;qBAAS;gBAC9D,EAAE,OAAO,cAAc;oBACrB,QAAQ,KAAK,CAAC,qCAAqC;gBACrD;gBACA,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY,OAAO,EAAE;YAC/D;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;YACf;YAEA;YACA,gBAAgB;YAChB,OAAO,IAAI,CAAC;QAEd,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,0BAA0B;YAExC,uCAAuC;YACvC,IAAI,eAAe;YAEnB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,sBAAsB;gBAC/C,eAAe,MAAM,OAAO;YAC9B,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAwB;gBACxD,eAAe,MAAM,OAAO;YAC9B,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,cAAc;gBAC9C,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAwB;gBACxD,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAwB;gBACxD,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAwB;;;;;;;;;;;;kCAI/C,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC;gBACC,UAAU,aAAa;gBACvB,WAAU;;kCAEV,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAQ;;;;;;kDACvB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACX,GAAG,SAAS,SAAS;4CAAE,UAAU;wCAAqB,EAAE;wCACzD,UAAU,aAAa;;;;;;oCAExB,OAAO,KAAK,kBACX,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0CAGjE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,aAAa;;0DAEvB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,aAAa;;0DAEvB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,kBAAkB,CAAC;;0DAE7B,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,iBAAiB,kBAAkB;;;;;;;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAO;;;;;;kDACtB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,aAAY;wCACX,GAAG,SAAS,QAAQ;4CAAE,UAAU;wCAAoB,EAAE;wCACvD,QAAQ;wCACR,WAAU;;;;;;oCAEX,OAAO,IAAI,kBACV,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,IAAI,CAAC,OAAO;;;;;;kDAE7D,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAGhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAU;;;;;;kDACzB,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,WAAU;wCACT,GAAG,SAAS,WAAW;4CAAE,UAAU;wCAAuB,EAAE;wCAC7D,UAAU,aAAa;;;;;;oCAExB,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;kDACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;0CAK7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAO;;;;;;0DACtB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,aAAY;gDACX,GAAG,SAAS,OAAO;gDACpB,UAAU,aAAa;;;;;;0DAEzB,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW;;;;;;0DAC1B,6LAAC,iKAAA,CAAA,aAAU;gDACT,MAAK;gDACL,SAAS;gDACT,OAAO;oDAAE,UAAU;gDAAwB;gDAC3C,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,qIAAA,CAAA,SAAM;wDACL,eAAe,MAAM,QAAQ;wDAC7B,cAAc,MAAM,KAAK;wDACzB,UAAU,aAAa;;0EAEvB,6LAAC,qIAAA,CAAA,gBAAa;gEAAC,IAAG;0EAChB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU;;;;;;kFAC5B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAkB;;;;;;kFAGpC,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAe;;;;;;kFACjC,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAe;;;;;;;;;;;;;;;;;;;;;;;4CAKvC,OAAO,QAAQ,kBACjB,6LAAC;gDAAE,WAAU;0DAA4B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAIpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAI,WAAU;;4CACZ,6BACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gIAAA,CAAA,UAAK;gEACJ,KAAK;gEACL,KAAI;gEACJ,IAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC,qIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS;0EAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAGjB,6LAAC;wDAAE,WAAU;kEAAiD;;;;;;kEAG9D,6LAAC;wDACC,SAAQ;wDACR,WAAU;kEACX;;;;;;;;;;;qEAKH,6LAAC;gDACC,SAAQ;gDACR,WAAW,CAAC,yHAAyH,EACnI,aACI,iCACA,4DACJ;gDACF,YAAY;gDACZ,aAAa;gDACb,QAAQ;0DAER,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAE,WAAU;;8EACX,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;gEAAsB;;;;;;;sEAGxD,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;0DAMnD,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,WAAU;gDACT,GAAG,SAAS,iBAAiB;oDAC5B,UAAU;oDACV,UAAU;gDACZ,EAAE;gDACF,UAAU,aAAa;;;;;;;;;;;;oCAGzB,OAAO,aAAa,kBACpB,6LAAC;wCAAE,WAAU;kDAA4B,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;kCAI3E,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU,aAAa;4BACvB,MAAK;sCAEJ,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAM3C;GA/iBgB;;QAKI,+HAAA,CAAA,WAAQ;QAEX,qIAAA,CAAA,YAAS;QAUpB,iKAAA,CAAA,UAAO;;;KAjBG", "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/admin/UploadDebugger.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { createClient } from \"@/lib/supabase/client\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Bug, Upload, Database, Key } from \"lucide-react\";\n\nexport function UploadDebugger() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [debugResults, setDebugResults] = useState<any[]>([]);\n  const { toast } = useToast();\n  const supabase = createClient();\n\n  const addResult = (test: string, success: boolean, data: any) => {\n    setDebugResults(prev => [...prev, { test, success, data, timestamp: new Date().toISOString() }]);\n  };\n\n  const runFullDiagnostic = async () => {\n    setIsLoading(true);\n    setDebugResults([]);\n\n    try {\n      // Test 1: Environment variables\n      addResult(\"Environment Variables\", true, {\n        supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,\n        supabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n        urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + \"...\"\n      });\n\n      // Test 2: Authentication\n      const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n      addResult(\"Authentication\", !!session && !sessionError, {\n        authenticated: !!session,\n        userId: session?.user?.id,\n        error: sessionError?.message\n      });\n\n      // Test 3: Storage buckets\n      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();\n      const postImagesBucket = buckets?.find(b => b.id === 'post_images');\n      addResult(\"Storage Buckets\", !bucketsError && !!postImagesBucket, {\n        totalBuckets: buckets?.length || 0,\n        postImagesBucket: !!postImagesBucket,\n        bucketConfig: postImagesBucket,\n        error: bucketsError?.message\n      });\n\n      // Test 4: Bucket permissions\n      if (postImagesBucket) {\n        const { data: files, error: listError } = await supabase.storage\n          .from('post_images')\n          .list('', { limit: 1 });\n        addResult(\"Bucket Permissions\", !listError, {\n          canList: !listError,\n          fileCount: files?.length || 0,\n          error: listError?.message\n        });\n      }\n\n      // Test 5: Test upload with dummy file\n      if (session && postImagesBucket) {\n        const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });\n        const testPath = `debug-test-${Date.now()}.txt`;\n        \n        const { error: uploadError } = await supabase.storage\n          .from('post_images')\n          .upload(testPath, testFile);\n        \n        if (!uploadError) {\n          // Clean up\n          await supabase.storage.from('post_images').remove([testPath]);\n        }\n        \n        addResult(\"Upload Test\", !uploadError, {\n          canUpload: !uploadError,\n          error: uploadError?.message\n        });\n      }\n\n      // Test 6: Database connection\n      const { data: posts, error: dbError } = await supabase\n        .from('posts')\n        .select('id')\n        .limit(1);\n      \n      addResult(\"Database Connection\", !dbError, {\n        canQuery: !dbError,\n        error: dbError?.message\n      });\n\n    } catch (error: any) {\n      addResult(\"General Error\", false, { error: error.message });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const createBucket = async () => {\n    try {\n      const { data, error } = await supabase.storage.createBucket('post_images', {\n        public: true,\n        fileSizeLimit: 5242880,\n        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']\n      });\n\n      if (error) {\n        toast({\n          title: \"Bucket Creation Failed\",\n          description: error.message,\n          variant: \"destructive\",\n        });\n      } else {\n        toast({\n          title: \"Bucket Created\",\n          description: \"post_images bucket created successfully\",\n        });\n      }\n    } catch (error: any) {\n      toast({\n        title: \"Error\",\n        description: error.message,\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-4xl\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Bug className=\"h-5 w-5\" />\n          Upload Diagnostics\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        <div className=\"flex gap-2\">\n          <Button onClick={runFullDiagnostic} disabled={isLoading}>\n            <Database className=\"mr-2 h-4 w-4\" />\n            {isLoading ? \"Running Tests...\" : \"Run Full Diagnostic\"}\n          </Button>\n          <Button onClick={createBucket} variant=\"outline\">\n            <Upload className=\"mr-2 h-4 w-4\" />\n            Create Bucket\n          </Button>\n        </div>\n\n        {debugResults.length > 0 && (\n          <div className=\"space-y-2\">\n            <h3 className=\"font-semibold\">Test Results:</h3>\n            {debugResults.map((result, index) => (\n              <div\n                key={index}\n                className={`p-3 rounded border ${\n                  result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'\n                }`}\n              >\n                <div className=\"flex items-center gap-2\">\n                  <span className={result.success ? 'text-green-600' : 'text-red-600'}>\n                    {result.success ? '✅' : '❌'}\n                  </span>\n                  <strong>{result.test}</strong>\n                </div>\n                <pre className=\"text-xs mt-1 overflow-auto\">\n                  {JSON.stringify(result.data, null, 2)}\n                </pre>\n              </div>\n            ))}\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;AA0BuB;;AAxBvB;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,YAAY,CAAC,MAAc,SAAkB;QACjD,gBAAgB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE;oBAAM;oBAAS;oBAAM,WAAW,IAAI,OAAO,WAAW;gBAAG;aAAE;IACjG;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,gBAAgB,EAAE;QAElB,IAAI;YACF,gCAAgC;YAChC,UAAU,yBAAyB,MAAM;gBACvC,aAAa,CAAC;gBACd,aAAa,CAAC;gBACd,UAAU,8EAAsC,UAAU,GAAG,MAAM;YACrE;YAEA,yBAAyB;YACzB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YACjF,UAAU,kBAAkB,CAAC,CAAC,WAAW,CAAC,cAAc;gBACtD,eAAe,CAAC,CAAC;gBACjB,QAAQ,SAAS,MAAM;gBACvB,OAAO,cAAc;YACvB;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,WAAW;YACjF,MAAM,mBAAmB,SAAS,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;YACrD,UAAU,mBAAmB,CAAC,gBAAgB,CAAC,CAAC,kBAAkB;gBAChE,cAAc,SAAS,UAAU;gBACjC,kBAAkB,CAAC,CAAC;gBACpB,cAAc;gBACd,OAAO,cAAc;YACvB;YAEA,6BAA6B;YAC7B,IAAI,kBAAkB;gBACpB,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,OAAO,CAC7D,IAAI,CAAC,eACL,IAAI,CAAC,IAAI;oBAAE,OAAO;gBAAE;gBACvB,UAAU,sBAAsB,CAAC,WAAW;oBAC1C,SAAS,CAAC;oBACV,WAAW,OAAO,UAAU;oBAC5B,OAAO,WAAW;gBACpB;YACF;YAEA,sCAAsC;YACtC,IAAI,WAAW,kBAAkB;gBAC/B,MAAM,WAAW,IAAI,KAAK;oBAAC;iBAAe,EAAE,YAAY;oBAAE,MAAM;gBAAa;gBAC7E,MAAM,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC;gBAE/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,OAAO,CAClD,IAAI,CAAC,eACL,MAAM,CAAC,UAAU;gBAEpB,IAAI,CAAC,aAAa;oBAChB,WAAW;oBACX,MAAM,SAAS,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,CAAC;wBAAC;qBAAS;gBAC9D;gBAEA,UAAU,eAAe,CAAC,aAAa;oBACrC,WAAW,CAAC;oBACZ,OAAO,aAAa;gBACtB;YACF;YAEA,8BAA8B;YAC9B,MAAM,EAAE,MAAM,KAAK,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAC3C,IAAI,CAAC,SACL,MAAM,CAAC,MACP,KAAK,CAAC;YAET,UAAU,uBAAuB,CAAC,SAAS;gBACzC,UAAU,CAAC;gBACX,OAAO,SAAS;YAClB;QAEF,EAAE,OAAO,OAAY;YACnB,UAAU,iBAAiB,OAAO;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC3D,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,OAAO,CAAC,YAAY,CAAC,eAAe;gBACzE,QAAQ;gBACR,eAAe;gBACf,kBAAkB;oBAAC;oBAAc;oBAAa;oBAAa;oBAAc;iBAAgB;YAC3F;YAEA,IAAI,OAAO;gBACT,MAAM;oBACJ,OAAO;oBACP,aAAa,MAAM,OAAO;oBAC1B,SAAS;gBACX;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;QACF,EAAE,OAAO,OAAY;YACnB,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAI/B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAmB,UAAU;;kDAC5C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,YAAY,qBAAqB;;;;;;;0CAEpC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAc,SAAQ;;kDACrC,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;oBAKtC,aAAa,MAAM,GAAG,mBACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;4BAC7B,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC;oCAEC,WAAW,CAAC,mBAAmB,EAC7B,OAAO,OAAO,GAAG,iCAAiC,4BAClD;;sDAEF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAW,OAAO,OAAO,GAAG,mBAAmB;8DAClD,OAAO,OAAO,GAAG,MAAM;;;;;;8DAE1B,6LAAC;8DAAQ,OAAO,IAAI;;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,OAAO,IAAI,EAAE,MAAM;;;;;;;mCAZhC;;;;;;;;;;;;;;;;;;;;;;;AAqBrB;GArKgB;;QAGI,+HAAA,CAAA,WAAQ;;;KAHZ", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/app/admin/posts/create/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { AdminSidebar } from \"@/components/admin/AdminSidebar\";\r\nimport { CreatePostForm } from \"@/components/admin/CreatePostForm\";\r\nimport { UploadDebugger } from \"@/components/admin/UploadDebugger\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\nimport { Rocket } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { createClient } from \"@/lib/supabase/client\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport type { User } from \"@supabase/supabase-js\";\r\n\r\nexport default function CreatePostPage() {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const supabase = createClient();\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    const checkUser = async () => {\r\n      const {\r\n        data: { session },\r\n      } = await supabase.auth.getSession();\r\n      if (session) {\r\n        setUser(session.user);\r\n      } else {\r\n        router.push(\"/login\");\r\n      }\r\n      setLoading(false);\r\n    };\r\n    checkUser();\r\n  }, [supabase, router]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        Loading...\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!user) {\r\n    return null; // A redirect is in progress\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-muted/40\">\r\n      <header className=\"bg-background border-b sticky top-0 z-10\">\r\n        <div className=\"container flex h-16 items-center justify-between\">\r\n          <Link href=\"/admin\" className=\"flex items-center gap-2\">\r\n            <Rocket className=\"h-6 w-6 text-primary\" />\r\n            <h1 className=\"text-xl font-bold font-headline text-foreground\">\r\n              Admin Dashboard\r\n            </h1>\r\n          </Link>\r\n          <Button variant=\"outline\" asChild>\r\n            <Link href=\"/\">View Site</Link>\r\n          </Button>\r\n        </div>\r\n      </header>\r\n      <div className=\"flex\">\r\n        <AdminSidebar />\r\n        <main className=\"flex-1 p-8 space-y-6\">\r\n          <UploadDebugger />\r\n          <CreatePostForm />\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;sDAAY;oBAChB,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAClC,IAAI,SAAS;wBACX,QAAQ,QAAQ,IAAI;oBACtB,OAAO;wBACL,OAAO,IAAI,CAAC;oBACd;oBACA,WAAW;gBACb;;YACA;QACF;mCAAG;QAAC;QAAU;KAAO;IAErB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,4BAA4B;IAC3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;;8CAC5B,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAG,WAAU;8CAAkD;;;;;;;;;;;;sCAIlE,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,OAAO;sCAC/B,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAI;;;;;;;;;;;;;;;;;;;;;;0BAIrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8IAAA,CAAA,eAAY;;;;;kCACb,6LAAC;wBAAK,WAAU;;0CACd,6LAAC,gJAAA,CAAA,iBAAc;;;;;0CACf,6LAAC,gJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GAzDwB;;QAIP,qIAAA,CAAA,YAAS;;;KAJF", "debugId": null}}]}