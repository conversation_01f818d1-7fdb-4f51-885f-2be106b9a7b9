"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { createClient } from "@/lib/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Bug, Upload, Database, Key } from "lucide-react";

export function UploadDebugger() {
  const [isLoading, setIsLoading] = useState(false);
  const [debugResults, setDebugResults] = useState<any[]>([]);
  const { toast } = useToast();
  const supabase = createClient();

  const addResult = (test: string, success: boolean, data: any) => {
    setDebugResults(prev => [...prev, { test, success, data, timestamp: new Date().toISOString() }]);
  };

  const runFullDiagnostic = async () => {
    setIsLoading(true);
    setDebugResults([]);

    try {
      // Test 1: Environment variables
      addResult("Environment Variables", true, {
        supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + "..."
      });

      // Test 2: Authentication
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      addResult("Authentication", !!session && !sessionError, {
        authenticated: !!session,
        userId: session?.user?.id,
        error: sessionError?.message
      });

      // Test 3: Storage buckets
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      const postImagesBucket = buckets?.find(b => b.id === 'post_images');
      addResult("Storage Buckets", !bucketsError && !!postImagesBucket, {
        totalBuckets: buckets?.length || 0,
        postImagesBucket: !!postImagesBucket,
        bucketConfig: postImagesBucket,
        error: bucketsError?.message
      });

      // Test 4: Bucket permissions
      if (postImagesBucket) {
        const { data: files, error: listError } = await supabase.storage
          .from('post_images')
          .list('', { limit: 1 });
        addResult("Bucket Permissions", !listError, {
          canList: !listError,
          fileCount: files?.length || 0,
          error: listError?.message
        });
      }

      // Test 5: Test upload with dummy file
      if (session && postImagesBucket) {
        const testFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
        const testPath = `debug-test-${Date.now()}.txt`;
        
        const { error: uploadError } = await supabase.storage
          .from('post_images')
          .upload(testPath, testFile);
        
        if (!uploadError) {
          // Clean up
          await supabase.storage.from('post_images').remove([testPath]);
        }
        
        addResult("Upload Test", !uploadError, {
          canUpload: !uploadError,
          error: uploadError?.message
        });
      }

      // Test 6: Database connection
      const { data: posts, error: dbError } = await supabase
        .from('posts')
        .select('id')
        .limit(1);
      
      addResult("Database Connection", !dbError, {
        canQuery: !dbError,
        error: dbError?.message
      });

    } catch (error: any) {
      addResult("General Error", false, { error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const createBucket = async () => {
    try {
      const { data, error } = await supabase.storage.createBucket('post_images', {
        public: true,
        fileSizeLimit: 5242880,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']
      });

      if (error) {
        toast({
          title: "Bucket Creation Failed",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Bucket Created",
          description: "post_images bucket created successfully",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Upload Diagnostics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={runFullDiagnostic} disabled={isLoading}>
            <Database className="mr-2 h-4 w-4" />
            {isLoading ? "Running Tests..." : "Run Full Diagnostic"}
          </Button>
          <Button onClick={createBucket} variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Create Bucket
          </Button>
        </div>

        {debugResults.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Test Results:</h3>
            {debugResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded border ${
                  result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}
              >
                <div className="flex items-center gap-2">
                  <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                    {result.success ? '✅' : '❌'}
                  </span>
                  <strong>{result.test}</strong>
                </div>
                <pre className="text-xs mt-1 overflow-auto">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
