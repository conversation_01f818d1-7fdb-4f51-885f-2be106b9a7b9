import { createClient } from './client';

export async function testStorageConnection() {
  const supabase = createClient();
  
  try {
    // Test 1: Check if user is authenticated
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    console.log('Session check:', { session: !!session, error: sessionError });
    
    if (!session) {
      return { success: false, error: 'Not authenticated' };
    }

    // Test 2: List buckets to verify storage access
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    console.log('Buckets:', { buckets, error: bucketsError });

    // Test 3: Check if post_images bucket exists
    const postImagesBucket = buckets?.find(bucket => bucket.id === 'post_images');
    console.log('Post images bucket:', postImagesBucket);

    // Test 4: Try to list files in the bucket
    const { data: files, error: filesError } = await supabase.storage
      .from('post_images')
      .list('', { limit: 5 });
    console.log('Files in bucket:', { files, error: filesError });

    return { 
      success: true, 
      data: { 
        authenticated: !!session,
        buckets: buckets?.length || 0,
        postImagesBucket: !!postImagesBucket,
        canListFiles: !filesError
      }
    };
  } catch (error) {
    console.error('Storage test error:', error);
    return { success: false, error: error.message };
  }
}

export async function testImageUpload(file: File) {
  const supabase = createClient();
  
  try {
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    const fileName = `test-${timestamp}.${fileExtension}`;
    
    console.log('Testing upload:', { fileName, size: file.size, type: file.type });

    const { data, error } = await supabase.storage
      .from('post_images')
      .upload(fileName, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error('Upload test failed:', error);
      return { success: false, error: error.message };
    }

    console.log('Upload test successful:', data);

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('post_images')
      .getPublicUrl(fileName);

    console.log('Public URL:', urlData?.publicUrl);

    // Clean up test file
    await supabase.storage.from('post_images').remove([fileName]);

    return { 
      success: true, 
      data: { 
        path: data.path,
        publicUrl: urlData?.publicUrl
      }
    };
  } catch (error) {
    console.error('Upload test error:', error);
    return { success: false, error: error.message };
  }
}
