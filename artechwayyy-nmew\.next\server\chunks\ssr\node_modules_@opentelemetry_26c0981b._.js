module.exports = {

"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
;
;
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/LogRecord.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "LogRecord": (()=>LogRecord)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/time.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$attributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/attributes.js [app-rsc] (ecmascript)");
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
;
;
;
var LogRecord = function() {
    function LogRecord(_sharedState, instrumentationScope, logRecord) {
        this.attributes = {};
        this.totalAttributesCount = 0;
        this._isReadonly = false;
        var timestamp = logRecord.timestamp, observedTimestamp = logRecord.observedTimestamp, severityNumber = logRecord.severityNumber, severityText = logRecord.severityText, body = logRecord.body, _a = logRecord.attributes, attributes = _a === void 0 ? {} : _a, context = logRecord.context;
        var now = Date.now();
        this.hrTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["timeInputToHrTime"])(timestamp !== null && timestamp !== void 0 ? timestamp : now);
        this.hrTimeObserved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["timeInputToHrTime"])(observedTimestamp !== null && observedTimestamp !== void 0 ? observedTimestamp : now);
        if (context) {
            var spanContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getSpanContext(context);
            if (spanContext && (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isSpanContextValid"])(spanContext)) {
                this.spanContext = spanContext;
            }
        }
        this.severityNumber = severityNumber;
        this.severityText = severityText;
        this.body = body;
        this.resource = _sharedState.resource;
        this.instrumentationScope = instrumentationScope;
        this._logRecordLimits = _sharedState.logRecordLimits;
        this.setAttributes(attributes);
    }
    Object.defineProperty(LogRecord.prototype, "severityText", {
        get: function() {
            return this._severityText;
        },
        set: function(severityText) {
            if (this._isLogRecordReadonly()) {
                return;
            }
            this._severityText = severityText;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LogRecord.prototype, "severityNumber", {
        get: function() {
            return this._severityNumber;
        },
        set: function(severityNumber) {
            if (this._isLogRecordReadonly()) {
                return;
            }
            this._severityNumber = severityNumber;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LogRecord.prototype, "body", {
        get: function() {
            return this._body;
        },
        set: function(body) {
            if (this._isLogRecordReadonly()) {
                return;
            }
            this._body = body;
        },
        enumerable: false,
        configurable: true
    });
    Object.defineProperty(LogRecord.prototype, "droppedAttributesCount", {
        get: function() {
            return this.totalAttributesCount - Object.keys(this.attributes).length;
        },
        enumerable: false,
        configurable: true
    });
    LogRecord.prototype.setAttribute = function(key, value) {
        if (this._isLogRecordReadonly()) {
            return this;
        }
        if (value === null) {
            return this;
        }
        if (key.length === 0) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn("Invalid attribute key: " + key);
            return this;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$attributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isAttributeValue"])(value) && !(typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length > 0)) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn("Invalid attribute value set for key: " + key);
            return this;
        }
        this.totalAttributesCount += 1;
        if (Object.keys(this.attributes).length >= this._logRecordLimits.attributeCountLimit && !Object.prototype.hasOwnProperty.call(this.attributes, key)) {
            // This logic is to create drop message at most once per LogRecord to prevent excessive logging.
            if (this.droppedAttributesCount === 1) {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Dropping extra attributes.');
            }
            return this;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$attributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isAttributeValue"])(value)) {
            this.attributes[key] = this._truncateToSize(value);
        } else {
            this.attributes[key] = value;
        }
        return this;
    };
    LogRecord.prototype.setAttributes = function(attributes) {
        var e_1, _a;
        try {
            for(var _b = __values(Object.entries(attributes)), _c = _b.next(); !_c.done; _c = _b.next()){
                var _d = __read(_c.value, 2), k = _d[0], v = _d[1];
                this.setAttribute(k, v);
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        return this;
    };
    LogRecord.prototype.setBody = function(body) {
        this.body = body;
        return this;
    };
    LogRecord.prototype.setSeverityNumber = function(severityNumber) {
        this.severityNumber = severityNumber;
        return this;
    };
    LogRecord.prototype.setSeverityText = function(severityText) {
        this.severityText = severityText;
        return this;
    };
    /**
     * @internal
     * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.
     * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.
     */ LogRecord.prototype._makeReadonly = function() {
        this._isReadonly = true;
    };
    LogRecord.prototype._truncateToSize = function(value) {
        var _this = this;
        var limit = this._logRecordLimits.attributeValueLengthLimit;
        // Check limit
        if (limit <= 0) {
            // Negative values are invalid, so do not truncate
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn("Attribute value limit must be positive, got " + limit);
            return value;
        }
        // String
        if (typeof value === 'string') {
            return this._truncateToLimitUtil(value, limit);
        }
        // Array of strings
        if (Array.isArray(value)) {
            return value.map(function(val) {
                return typeof val === 'string' ? _this._truncateToLimitUtil(val, limit) : val;
            });
        }
        // Other types, no need to apply value length limit
        return value;
    };
    LogRecord.prototype._truncateToLimitUtil = function(value, limit) {
        if (value.length <= limit) {
            return value;
        }
        return value.substring(0, limit);
    };
    LogRecord.prototype._isLogRecordReadonly = function() {
        if (this._isReadonly) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Can not execute the operation on emitted log record');
        }
        return this._isReadonly;
    };
    return LogRecord;
}();
;
 //# sourceMappingURL=LogRecord.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/Logger.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "Logger": (()=>Logger)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/LogRecord.js [app-rsc] (ecmascript)");
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
var Logger = function() {
    function Logger(instrumentationScope, _sharedState) {
        this.instrumentationScope = instrumentationScope;
        this._sharedState = _sharedState;
    }
    Logger.prototype.emit = function(logRecord) {
        var currentContext = logRecord.context || __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["context"].active();
        /**
         * If a Logger was obtained with include_trace_context=true,
         * the LogRecords it emits MUST automatically include the Trace Context from the active Context,
         * if Context has not been explicitly set.
         */ var logRecordInstance = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LogRecord"](this._sharedState, this.instrumentationScope, __assign({
            context: currentContext
        }, logRecord));
        /**
         * the explicitly passed Context,
         * the current Context, or an empty Context if the Logger was obtained with include_trace_context=false
         */ this._sharedState.activeProcessor.onEmit(logRecordInstance, currentContext);
        /**
         * A LogRecordProcessor may freely modify logRecord for the duration of the OnEmit call.
         * If logRecord is needed after OnEmit returns (i.e. for asynchronous processing) only reads are permitted.
         */ logRecordInstance._makeReadonly();
    };
    return Logger;
}();
;
 //# sourceMappingURL=Logger.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/config.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "loadDefaultConfig": (()=>loadDefaultConfig),
    "reconfigureLimits": (()=>reconfigureLimits)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
;
function loadDefaultConfig() {
    return {
        forceFlushTimeoutMillis: 30000,
        logRecordLimits: {
            attributeValueLengthLimit: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,
            attributeCountLimit: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT
        },
        includeTraceContext: true
    };
}
function reconfigureLimits(logRecordLimits) {
    var _a, _b, _c, _d, _e, _f;
    var parsedEnvConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnvWithoutDefaults"])();
    return {
        /**
         * Reassign log record attribute count limit to use first non null value defined by user or use default value
         */ attributeCountLimit: (_c = (_b = (_a = logRecordLimits.attributeCountLimit) !== null && _a !== void 0 ? _a : parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT) !== null && _b !== void 0 ? _b : parsedEnvConfig.OTEL_ATTRIBUTE_COUNT_LIMIT) !== null && _c !== void 0 ? _c : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_ATTRIBUTE_COUNT_LIMIT"],
        /**
         * Reassign log record attribute value length limit to use first non null value defined by user or use default value
         */ attributeValueLengthLimit: (_f = (_e = (_d = logRecordLimits.attributeValueLengthLimit) !== null && _d !== void 0 ? _d : parsedEnvConfig.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && _e !== void 0 ? _e : parsedEnvConfig.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT) !== null && _f !== void 0 ? _f : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_ATTRIBUTE_VALUE_LENGTH_LIMIT"]
    };
} //# sourceMappingURL=config.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/MultiLogRecordProcessor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "MultiLogRecordProcessor": (()=>MultiLogRecordProcessor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$timeout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/timeout.js [app-rsc] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
/**
 * Implementation of the {@link LogRecordProcessor} that simply forwards all
 * received events to a list of {@link LogRecordProcessor}s.
 */ var MultiLogRecordProcessor = function() {
    function MultiLogRecordProcessor(processors, forceFlushTimeoutMillis) {
        this.processors = processors;
        this.forceFlushTimeoutMillis = forceFlushTimeoutMillis;
    }
    MultiLogRecordProcessor.prototype.forceFlush = function() {
        return __awaiter(this, void 0, void 0, function() {
            var timeout;
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        timeout = this.forceFlushTimeoutMillis;
                        return [
                            4 /*yield*/ ,
                            Promise.all(this.processors.map(function(processor) {
                                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$timeout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["callWithTimeout"])(processor.forceFlush(), timeout);
                            }))
                        ];
                    case 1:
                        _a.sent();
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    MultiLogRecordProcessor.prototype.onEmit = function(logRecord, context) {
        this.processors.forEach(function(processors) {
            return processors.onEmit(logRecord, context);
        });
    };
    MultiLogRecordProcessor.prototype.shutdown = function() {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        return [
                            4 /*yield*/ ,
                            Promise.all(this.processors.map(function(processor) {
                                return processor.shutdown();
                            }))
                        ];
                    case 1:
                        _a.sent();
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    return MultiLogRecordProcessor;
}();
;
 //# sourceMappingURL=MultiLogRecordProcessor.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/NoopLogRecordProcessor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "NoopLogRecordProcessor": (()=>NoopLogRecordProcessor)
});
var NoopLogRecordProcessor = function() {
    function NoopLogRecordProcessor() {}
    NoopLogRecordProcessor.prototype.forceFlush = function() {
        return Promise.resolve();
    };
    NoopLogRecordProcessor.prototype.onEmit = function(_logRecord, _context) {};
    NoopLogRecordProcessor.prototype.shutdown = function() {
        return Promise.resolve();
    };
    return NoopLogRecordProcessor;
}();
;
 //# sourceMappingURL=NoopLogRecordProcessor.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/internal/LoggerProviderSharedState.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "LoggerProviderSharedState": (()=>LoggerProviderSharedState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$NoopLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/NoopLogRecordProcessor.js [app-rsc] (ecmascript)");
;
var LoggerProviderSharedState = function() {
    function LoggerProviderSharedState(resource, forceFlushTimeoutMillis, logRecordLimits) {
        this.resource = resource;
        this.forceFlushTimeoutMillis = forceFlushTimeoutMillis;
        this.logRecordLimits = logRecordLimits;
        this.loggers = new Map();
        this.registeredLogRecordProcessors = [];
        this.activeProcessor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$NoopLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoopLogRecordProcessor"]();
    }
    return LoggerProviderSharedState;
}();
;
 //# sourceMappingURL=LoggerProviderSharedState.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/LoggerProvider.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "DEFAULT_LOGGER_NAME": (()=>DEFAULT_LOGGER_NAME),
    "LoggerProvider": (()=>LoggerProvider)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$resources$2f$build$2f$esm$2f$Resource$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/resources/build/esm/Resource.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/callback.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$merge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/merge.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$Logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/Logger.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/config.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$MultiLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/MultiLogRecordProcessor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$internal$2f$LoggerProviderSharedState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/internal/LoggerProviderSharedState.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
var DEFAULT_LOGGER_NAME = 'unknown';
var LoggerProvider = function() {
    function LoggerProvider(config) {
        if (config === void 0) {
            config = {};
        }
        var _a;
        var mergedConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$merge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["merge"])({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["loadDefaultConfig"])(), config);
        var resource = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$resources$2f$build$2f$esm$2f$Resource$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Resource"].default().merge((_a = mergedConfig.resource) !== null && _a !== void 0 ? _a : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$resources$2f$build$2f$esm$2f$Resource$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Resource"].empty());
        this._sharedState = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$internal$2f$LoggerProviderSharedState$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LoggerProviderSharedState"](resource, mergedConfig.forceFlushTimeoutMillis, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$config$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["reconfigureLimits"])(mergedConfig.logRecordLimits));
        this._shutdownOnce = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BindOnceFuture"](this._shutdown, this);
    }
    /**
     * Get a logger with the configuration of the LoggerProvider.
     */ LoggerProvider.prototype.getLogger = function(name, version, options) {
        if (this._shutdownOnce.isCalled) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('A shutdown LoggerProvider cannot provide a Logger');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NOOP_LOGGER"];
        }
        if (!name) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Logger requested without instrumentation scope name.');
        }
        var loggerName = name || DEFAULT_LOGGER_NAME;
        var key = loggerName + "@" + (version || '') + ":" + ((options === null || options === void 0 ? void 0 : options.schemaUrl) || '');
        if (!this._sharedState.loggers.has(key)) {
            this._sharedState.loggers.set(key, new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$Logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Logger"]({
                name: loggerName,
                version: version,
                schemaUrl: options === null || options === void 0 ? void 0 : options.schemaUrl
            }, this._sharedState));
        }
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        return this._sharedState.loggers.get(key);
    };
    /**
     * Adds a new {@link LogRecordProcessor} to this logger.
     * @param processor the new LogRecordProcessor to be added.
     */ LoggerProvider.prototype.addLogRecordProcessor = function(processor) {
        if (this._sharedState.registeredLogRecordProcessors.length === 0) {
            // since we might have enabled by default a batchProcessor, we disable it
            // before adding the new one
            this._sharedState.activeProcessor.shutdown().catch(function(err) {
                return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error('Error while trying to shutdown current log record processor', err);
            });
        }
        this._sharedState.registeredLogRecordProcessors.push(processor);
        this._sharedState.activeProcessor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$MultiLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MultiLogRecordProcessor"](this._sharedState.registeredLogRecordProcessors, this._sharedState.forceFlushTimeoutMillis);
    };
    /**
     * Notifies all registered LogRecordProcessor to flush any buffered data.
     *
     * Returns a promise which is resolved when all flushes are complete.
     */ LoggerProvider.prototype.forceFlush = function() {
        // do not flush after shutdown
        if (this._shutdownOnce.isCalled) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('invalid attempt to force flush after LoggerProvider shutdown');
            return this._shutdownOnce.promise;
        }
        return this._sharedState.activeProcessor.forceFlush();
    };
    /**
     * Flush all buffered data and shut down the LoggerProvider and all registered
     * LogRecordProcessor.
     *
     * Returns a promise which is resolved when all flushes are complete.
     */ LoggerProvider.prototype.shutdown = function() {
        if (this._shutdownOnce.isCalled) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('shutdown may only be called once per LoggerProvider');
            return this._shutdownOnce.promise;
        }
        return this._shutdownOnce.call();
    };
    LoggerProvider.prototype._shutdown = function() {
        return this._sharedState.activeProcessor.shutdown();
    };
    return LoggerProvider;
}();
;
 //# sourceMappingURL=LoggerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/ConsoleLogRecordExporter.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ConsoleLogRecordExporter": (()=>ConsoleLogRecordExporter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/time.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
/**
 * This is implementation of {@link LogRecordExporter} that prints LogRecords to the
 * console. This class can be used for diagnostic purposes.
 */ /* eslint-disable no-console */ var ConsoleLogRecordExporter = function() {
    function ConsoleLogRecordExporter() {}
    /**
     * Export logs.
     * @param logs
     * @param resultCallback
     */ ConsoleLogRecordExporter.prototype.export = function(logs, resultCallback) {
        this._sendLogRecords(logs, resultCallback);
    };
    /**
     * Shutdown the exporter.
     */ ConsoleLogRecordExporter.prototype.shutdown = function() {
        return Promise.resolve();
    };
    /**
     * converts logRecord info into more readable format
     * @param logRecord
     */ ConsoleLogRecordExporter.prototype._exportInfo = function(logRecord) {
        var _a, _b, _c;
        return {
            resource: {
                attributes: logRecord.resource.attributes
            },
            timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hrTimeToMicroseconds"])(logRecord.hrTime),
            traceId: (_a = logRecord.spanContext) === null || _a === void 0 ? void 0 : _a.traceId,
            spanId: (_b = logRecord.spanContext) === null || _b === void 0 ? void 0 : _b.spanId,
            traceFlags: (_c = logRecord.spanContext) === null || _c === void 0 ? void 0 : _c.traceFlags,
            severityText: logRecord.severityText,
            severityNumber: logRecord.severityNumber,
            body: logRecord.body,
            attributes: logRecord.attributes
        };
    };
    /**
     * Showing logs  in console
     * @param logRecords
     * @param done
     */ ConsoleLogRecordExporter.prototype._sendLogRecords = function(logRecords, done) {
        var e_1, _a;
        try {
            for(var logRecords_1 = __values(logRecords), logRecords_1_1 = logRecords_1.next(); !logRecords_1_1.done; logRecords_1_1 = logRecords_1.next()){
                var logRecord = logRecords_1_1.value;
                console.dir(this._exportInfo(logRecord), {
                    depth: 3
                });
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (logRecords_1_1 && !logRecords_1_1.done && (_a = logRecords_1.return)) _a.call(logRecords_1);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        done === null || done === void 0 ? void 0 : done({
            code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS
        });
    };
    return ConsoleLogRecordExporter;
}();
;
 //# sourceMappingURL=ConsoleLogRecordExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/SimpleLogRecordProcessor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "SimpleLogRecordProcessor": (()=>SimpleLogRecordProcessor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/callback.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/global-error-handler.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript) <locals>");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
var SimpleLogRecordProcessor = function() {
    function SimpleLogRecordProcessor(_exporter) {
        this._exporter = _exporter;
        this._shutdownOnce = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BindOnceFuture"](this._shutdown, this);
        this._unresolvedExports = new Set();
    }
    SimpleLogRecordProcessor.prototype.onEmit = function(logRecord) {
        var _this = this;
        var _a, _b;
        if (this._shutdownOnce.isCalled) {
            return;
        }
        var doExport = function() {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["internal"]._export(_this._exporter, [
                logRecord
            ]).then(function(result) {
                var _a;
                if (result.code !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"])((_a = result.error) !== null && _a !== void 0 ? _a : new Error("SimpleLogRecordProcessor: log record export failed (status " + result + ")"));
                }
            }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"]);
        };
        // Avoid scheduling a promise to make the behavior more predictable and easier to test
        if (logRecord.resource.asyncAttributesPending) {
            var exportPromise_1 = (_b = (_a = logRecord.resource).waitForAsyncAttributes) === null || _b === void 0 ? void 0 : _b.call(_a).then(function() {
                // Using TS Non-null assertion operator because exportPromise could not be null in here
                // if waitForAsyncAttributes is not present this code will never be reached
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                _this._unresolvedExports.delete(exportPromise_1);
                return doExport();
            }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"]);
            // store the unresolved exports
            if (exportPromise_1 != null) {
                this._unresolvedExports.add(exportPromise_1);
            }
        } else {
            void doExport();
        }
    };
    SimpleLogRecordProcessor.prototype.forceFlush = function() {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        // await unresolved resources before resolving
                        return [
                            4 /*yield*/ ,
                            Promise.all(Array.from(this._unresolvedExports))
                        ];
                    case 1:
                        // await unresolved resources before resolving
                        _a.sent();
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    SimpleLogRecordProcessor.prototype.shutdown = function() {
        return this._shutdownOnce.call();
    };
    SimpleLogRecordProcessor.prototype._shutdown = function() {
        return this._exporter.shutdown();
    };
    return SimpleLogRecordProcessor;
}();
;
 //# sourceMappingURL=SimpleLogRecordProcessor.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/InMemoryLogRecordExporter.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "InMemoryLogRecordExporter": (()=>InMemoryLogRecordExporter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from, pack) {
    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
;
/**
 * This class can be used for testing purposes. It stores the exported LogRecords
 * in a list in memory that can be retrieved using the `getFinishedLogRecords()`
 * method.
 */ var InMemoryLogRecordExporter = function() {
    function InMemoryLogRecordExporter() {
        this._finishedLogRecords = [];
        /**
         * Indicates if the exporter has been "shutdown."
         * When false, exported log records will not be stored in-memory.
         */ this._stopped = false;
    }
    InMemoryLogRecordExporter.prototype.export = function(logs, resultCallback) {
        var _a;
        if (this._stopped) {
            return resultCallback({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                error: new Error('Exporter has been stopped')
            });
        }
        (_a = this._finishedLogRecords).push.apply(_a, __spreadArray([], __read(logs), false));
        resultCallback({
            code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS
        });
    };
    InMemoryLogRecordExporter.prototype.shutdown = function() {
        this._stopped = true;
        this.reset();
        return Promise.resolve();
    };
    InMemoryLogRecordExporter.prototype.getFinishedLogRecords = function() {
        return this._finishedLogRecords;
    };
    InMemoryLogRecordExporter.prototype.reset = function() {
        this._finishedLogRecords = [];
    };
    return InMemoryLogRecordExporter;
}();
;
 //# sourceMappingURL=InMemoryLogRecordExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/BatchLogRecordProcessorBase.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "BatchLogRecordProcessorBase": (()=>BatchLogRecordProcessorBase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/global-error-handler.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$timer$2d$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/timer-util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/callback.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$timeout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/timeout.js [app-rsc] (ecmascript)");
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
;
;
var BatchLogRecordProcessorBase = function() {
    function BatchLogRecordProcessorBase(_exporter, config) {
        var _a, _b, _c, _d;
        this._exporter = _exporter;
        this._finishedLogRecords = [];
        var env = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])();
        this._maxExportBatchSize = (_a = config === null || config === void 0 ? void 0 : config.maxExportBatchSize) !== null && _a !== void 0 ? _a : env.OTEL_BLRP_MAX_EXPORT_BATCH_SIZE;
        this._maxQueueSize = (_b = config === null || config === void 0 ? void 0 : config.maxQueueSize) !== null && _b !== void 0 ? _b : env.OTEL_BLRP_MAX_QUEUE_SIZE;
        this._scheduledDelayMillis = (_c = config === null || config === void 0 ? void 0 : config.scheduledDelayMillis) !== null && _c !== void 0 ? _c : env.OTEL_BLRP_SCHEDULE_DELAY;
        this._exportTimeoutMillis = (_d = config === null || config === void 0 ? void 0 : config.exportTimeoutMillis) !== null && _d !== void 0 ? _d : env.OTEL_BLRP_EXPORT_TIMEOUT;
        this._shutdownOnce = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BindOnceFuture"](this._shutdown, this);
        if (this._maxExportBatchSize > this._maxQueueSize) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize');
            this._maxExportBatchSize = this._maxQueueSize;
        }
    }
    BatchLogRecordProcessorBase.prototype.onEmit = function(logRecord) {
        if (this._shutdownOnce.isCalled) {
            return;
        }
        this._addToBuffer(logRecord);
    };
    BatchLogRecordProcessorBase.prototype.forceFlush = function() {
        if (this._shutdownOnce.isCalled) {
            return this._shutdownOnce.promise;
        }
        return this._flushAll();
    };
    BatchLogRecordProcessorBase.prototype.shutdown = function() {
        return this._shutdownOnce.call();
    };
    BatchLogRecordProcessorBase.prototype._shutdown = function() {
        return __awaiter(this, void 0, void 0, function() {
            return __generator(this, function(_a) {
                switch(_a.label){
                    case 0:
                        this.onShutdown();
                        return [
                            4 /*yield*/ ,
                            this._flushAll()
                        ];
                    case 1:
                        _a.sent();
                        return [
                            4 /*yield*/ ,
                            this._exporter.shutdown()
                        ];
                    case 2:
                        _a.sent();
                        return [
                            2 /*return*/ 
                        ];
                }
            });
        });
    };
    /** Add a LogRecord in the buffer. */ BatchLogRecordProcessorBase.prototype._addToBuffer = function(logRecord) {
        if (this._finishedLogRecords.length >= this._maxQueueSize) {
            return;
        }
        this._finishedLogRecords.push(logRecord);
        this._maybeStartTimer();
    };
    /**
     * Send all LogRecords to the exporter respecting the batch size limit
     * This function is used only on forceFlush or shutdown,
     * for all other cases _flush should be used
     * */ BatchLogRecordProcessorBase.prototype._flushAll = function() {
        var _this = this;
        return new Promise(function(resolve, reject) {
            var promises = [];
            var batchCount = Math.ceil(_this._finishedLogRecords.length / _this._maxExportBatchSize);
            for(var i = 0; i < batchCount; i++){
                promises.push(_this._flushOneBatch());
            }
            Promise.all(promises).then(function() {
                resolve();
            }).catch(reject);
        });
    };
    BatchLogRecordProcessorBase.prototype._flushOneBatch = function() {
        var _this = this;
        this._clearTimer();
        if (this._finishedLogRecords.length === 0) {
            return Promise.resolve();
        }
        return new Promise(function(resolve, reject) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$timeout$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["callWithTimeout"])(_this._export(_this._finishedLogRecords.splice(0, _this._maxExportBatchSize)), _this._exportTimeoutMillis).then(function() {
                return resolve();
            }).catch(reject);
        });
    };
    BatchLogRecordProcessorBase.prototype._maybeStartTimer = function() {
        var _this = this;
        if (this._timer !== undefined) {
            return;
        }
        this._timer = setTimeout(function() {
            _this._flushOneBatch().then(function() {
                if (_this._finishedLogRecords.length > 0) {
                    _this._clearTimer();
                    _this._maybeStartTimer();
                }
            }).catch(function(e) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"])(e);
            });
        }, this._scheduledDelayMillis);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$timer$2d$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unrefTimer"])(this._timer);
    };
    BatchLogRecordProcessorBase.prototype._clearTimer = function() {
        if (this._timer !== undefined) {
            clearTimeout(this._timer);
            this._timer = undefined;
        }
    };
    BatchLogRecordProcessorBase.prototype._export = function(logRecords) {
        var _this = this;
        var doExport = function() {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["internal"]._export(_this._exporter, logRecords).then(function(result) {
                var _a;
                if (result.code !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"])((_a = result.error) !== null && _a !== void 0 ? _a : new Error("BatchLogRecordProcessor: log record export failed (status " + result + ")"));
                }
            }).catch(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"]);
        };
        var pendingResources = logRecords.map(function(logRecord) {
            return logRecord.resource;
        }).filter(function(resource) {
            return resource.asyncAttributesPending;
        });
        // Avoid scheduling a promise to make the behavior more predictable and easier to test
        if (pendingResources.length === 0) {
            return doExport();
        } else {
            return Promise.all(pendingResources.map(function(resource) {
                var _a;
                return (_a = resource.waitForAsyncAttributes) === null || _a === void 0 ? void 0 : _a.call(resource);
            })).then(doExport, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$global$2d$error$2d$handler$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["globalErrorHandler"]);
        }
    };
    return BatchLogRecordProcessorBase;
}();
;
 //# sourceMappingURL=BatchLogRecordProcessorBase.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/platform/node/export/BatchLogRecordProcessor.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "BatchLogRecordProcessor": (()=>BatchLogRecordProcessor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$BatchLogRecordProcessorBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/BatchLogRecordProcessorBase.js [app-rsc] (ecmascript)");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
var BatchLogRecordProcessor = function(_super) {
    __extends(BatchLogRecordProcessor, _super);
    function BatchLogRecordProcessor() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    BatchLogRecordProcessor.prototype.onShutdown = function() {};
    return BatchLogRecordProcessor;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$BatchLogRecordProcessorBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BatchLogRecordProcessorBase"]);
;
 //# sourceMappingURL=BatchLogRecordProcessor.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BatchLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$platform$2f$node$2f$export$2f$BatchLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BatchLogRecordProcessor"]),
    "ConsoleLogRecordExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$ConsoleLogRecordExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ConsoleLogRecordExporter"]),
    "InMemoryLogRecordExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$InMemoryLogRecordExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InMemoryLogRecordExporter"]),
    "LogRecord": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LogRecord"]),
    "LoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LoggerProvider"]),
    "NoopLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$NoopLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoopLogRecordProcessor"]),
    "SimpleLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$SimpleLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SimpleLogRecordProcessor"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/LoggerProvider.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/LogRecord.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$NoopLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/NoopLogRecordProcessor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$ConsoleLogRecordExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/ConsoleLogRecordExporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$SimpleLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/SimpleLogRecordProcessor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$export$2f$InMemoryLogRecordExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/export/InMemoryLogRecordExporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$platform$2f$node$2f$export$2f$BatchLogRecordProcessor$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/platform/node/export/BatchLogRecordProcessor.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BatchLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["BatchLogRecordProcessor"]),
    "ConsoleLogRecordExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ConsoleLogRecordExporter"]),
    "InMemoryLogRecordExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InMemoryLogRecordExporter"]),
    "LogRecord": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["LogRecord"]),
    "LoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["LoggerProvider"]),
    "NoopLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NoopLogRecordProcessor"]),
    "SimpleLogRecordProcessor": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SimpleLogRecordProcessor"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$sdk$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "NOOP_LOGGER": (()=>NOOP_LOGGER),
    "NoopLogger": (()=>NoopLogger)
});
var NoopLogger = function() {
    function NoopLogger() {}
    NoopLogger.prototype.emit = function(_logRecord) {};
    return NoopLogger;
}();
;
var NOOP_LOGGER = new NoopLogger(); //# sourceMappingURL=NoopLogger.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/platform/node/globalThis.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /** only globals that common to node and browsers are allowed */ // eslint-disable-next-line node/no-unsupported-features/es-builtins
__turbopack_context__.s({
    "_globalThis": (()=>_globalThis)
});
var _globalThis = typeof globalThis === 'object' ? globalThis : global; //# sourceMappingURL=globalThis.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/internal/global-utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "API_BACKWARDS_COMPATIBILITY_VERSION": (()=>API_BACKWARDS_COMPATIBILITY_VERSION),
    "GLOBAL_LOGS_API_KEY": (()=>GLOBAL_LOGS_API_KEY),
    "_global": (()=>_global),
    "makeGetter": (()=>makeGetter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$platform$2f$node$2f$globalThis$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/platform/node/globalThis.js [app-rsc] (ecmascript)");
;
var GLOBAL_LOGS_API_KEY = Symbol.for('io.opentelemetry.js.api.logs');
var _global = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$platform$2f$node$2f$globalThis$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_globalThis"];
function makeGetter(requiredVersion, instance, fallback) {
    return function(version) {
        return version === requiredVersion ? instance : fallback;
    };
}
var API_BACKWARDS_COMPATIBILITY_VERSION = 1; //# sourceMappingURL=global-utils.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "NOOP_LOGGER_PROVIDER": (()=>NOOP_LOGGER_PROVIDER),
    "NoopLoggerProvider": (()=>NoopLoggerProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [app-rsc] (ecmascript)");
;
var NoopLoggerProvider = function() {
    function NoopLoggerProvider() {}
    NoopLoggerProvider.prototype.getLogger = function(_name, _version, _options) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoopLogger"]();
    };
    return NoopLoggerProvider;
}();
;
var NOOP_LOGGER_PROVIDER = new NoopLoggerProvider(); //# sourceMappingURL=NoopLoggerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/api/logs.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "LogsAPI": (()=>LogsAPI)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/internal/global-utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [app-rsc] (ecmascript)");
;
;
var LogsAPI = function() {
    function LogsAPI() {}
    LogsAPI.getInstance = function() {
        if (!this._instance) {
            this._instance = new LogsAPI();
        }
        return this._instance;
    };
    LogsAPI.prototype.setGlobalLoggerProvider = function(provider) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]]) {
            return this.getLoggerProvider();
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["makeGetter"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["API_BACKWARDS_COMPATIBILITY_VERSION"], provider, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"]);
        return provider;
    };
    /**
     * Returns the global logger provider.
     *
     * @returns LoggerProvider
     */ LogsAPI.prototype.getLoggerProvider = function() {
        var _a, _b;
        return (_b = (_a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]]) === null || _a === void 0 ? void 0 : _a.call(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_global"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["API_BACKWARDS_COMPATIBILITY_VERSION"])) !== null && _b !== void 0 ? _b : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"];
    };
    /**
     * Returns a logger from the global logger provider.
     *
     * @returns Logger
     */ LogsAPI.prototype.getLogger = function(name, version, options) {
        return this.getLoggerProvider().getLogger(name, version, options);
    };
    /** Remove the global logger provider */ LogsAPI.prototype.disable = function() {
        delete __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["_global"][__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$internal$2f$global$2d$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GLOBAL_LOGS_API_KEY"]];
    };
    return LogsAPI;
}();
;
 //# sourceMappingURL=logs.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "logs": (()=>logs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$api$2f$logs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/api/logs.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
var logs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$api$2f$logs$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["LogsAPI"].getInstance(); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/Logger.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=Logger.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LoggerProvider.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=LoggerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LogRecord.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "SeverityNumber": (()=>SeverityNumber)
});
var SeverityNumber;
(function(SeverityNumber) {
    SeverityNumber[SeverityNumber["UNSPECIFIED"] = 0] = "UNSPECIFIED";
    SeverityNumber[SeverityNumber["TRACE"] = 1] = "TRACE";
    SeverityNumber[SeverityNumber["TRACE2"] = 2] = "TRACE2";
    SeverityNumber[SeverityNumber["TRACE3"] = 3] = "TRACE3";
    SeverityNumber[SeverityNumber["TRACE4"] = 4] = "TRACE4";
    SeverityNumber[SeverityNumber["DEBUG"] = 5] = "DEBUG";
    SeverityNumber[SeverityNumber["DEBUG2"] = 6] = "DEBUG2";
    SeverityNumber[SeverityNumber["DEBUG3"] = 7] = "DEBUG3";
    SeverityNumber[SeverityNumber["DEBUG4"] = 8] = "DEBUG4";
    SeverityNumber[SeverityNumber["INFO"] = 9] = "INFO";
    SeverityNumber[SeverityNumber["INFO2"] = 10] = "INFO2";
    SeverityNumber[SeverityNumber["INFO3"] = 11] = "INFO3";
    SeverityNumber[SeverityNumber["INFO4"] = 12] = "INFO4";
    SeverityNumber[SeverityNumber["WARN"] = 13] = "WARN";
    SeverityNumber[SeverityNumber["WARN2"] = 14] = "WARN2";
    SeverityNumber[SeverityNumber["WARN3"] = 15] = "WARN3";
    SeverityNumber[SeverityNumber["WARN4"] = 16] = "WARN4";
    SeverityNumber[SeverityNumber["ERROR"] = 17] = "ERROR";
    SeverityNumber[SeverityNumber["ERROR2"] = 18] = "ERROR2";
    SeverityNumber[SeverityNumber["ERROR3"] = 19] = "ERROR3";
    SeverityNumber[SeverityNumber["ERROR4"] = 20] = "ERROR4";
    SeverityNumber[SeverityNumber["FATAL"] = 21] = "FATAL";
    SeverityNumber[SeverityNumber["FATAL2"] = 22] = "FATAL2";
    SeverityNumber[SeverityNumber["FATAL3"] = 23] = "FATAL3";
    SeverityNumber[SeverityNumber["FATAL4"] = 24] = "FATAL4";
})(SeverityNumber || (SeverityNumber = {})); //# sourceMappingURL=LogRecord.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LoggerOptions.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
 //# sourceMappingURL=LoggerOptions.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/types/AnyValue.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=AnyValue.js.map
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NOOP_LOGGER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NOOP_LOGGER"]),
    "NOOP_LOGGER_PROVIDER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NOOP_LOGGER_PROVIDER"]),
    "NoopLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoopLogger"]),
    "NoopLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["NoopLoggerProvider"]),
    "SeverityNumber": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SeverityNumber"]),
    "logs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["logs"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$Logger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/Logger.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LoggerProvider.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LogRecord$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LogRecord.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$LoggerOptions$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/LoggerOptions.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$types$2f$AnyValue$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/types/AnyValue.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLogger$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLogger.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$NoopLoggerProvider$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/NoopLoggerProvider.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "NOOP_LOGGER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NOOP_LOGGER"]),
    "NOOP_LOGGER_PROVIDER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NOOP_LOGGER_PROVIDER"]),
    "NoopLogger": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NoopLogger"]),
    "NoopLoggerProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NoopLoggerProvider"]),
    "SeverityNumber": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SeverityNumber"]),
    "logs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["logs"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AbstractAsyncHooksContextManager = void 0;
const events_1 = __turbopack_context__.r("[externals]/events [external] (events, cjs)");
const ADD_LISTENER_METHODS = [
    'addListener',
    'on',
    'once',
    'prependListener',
    'prependOnceListener'
];
class AbstractAsyncHooksContextManager {
    constructor(){
        this._kOtListeners = Symbol('OtListeners');
        this._wrapped = false;
    }
    /**
     * Binds a the certain context or the active one to the target function and then returns the target
     * @param context A context (span) to be bind to target
     * @param target a function or event emitter. When target or one of its callbacks is called,
     *  the provided context will be used as the active context for the duration of the call.
     */ bind(context, target) {
        if (target instanceof events_1.EventEmitter) {
            return this._bindEventEmitter(context, target);
        }
        if (typeof target === 'function') {
            return this._bindFunction(context, target);
        }
        return target;
    }
    _bindFunction(context, target) {
        const manager = this;
        const contextWrapper = function(...args) {
            return manager.with(context, ()=>target.apply(this, args));
        };
        Object.defineProperty(contextWrapper, 'length', {
            enumerable: false,
            configurable: true,
            writable: false,
            value: target.length
        });
        /**
         * It isn't possible to tell Typescript that contextWrapper is the same as T
         * so we forced to cast as any here.
         */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
        return contextWrapper;
    }
    /**
     * By default, EventEmitter call their callback with their context, which we do
     * not want, instead we will bind a specific context to all callbacks that
     * go through it.
     * @param context the context we want to bind
     * @param ee EventEmitter an instance of EventEmitter to patch
     */ _bindEventEmitter(context, ee) {
        const map = this._getPatchMap(ee);
        if (map !== undefined) return ee;
        this._createPatchMap(ee);
        // patch methods that add a listener to propagate context
        ADD_LISTENER_METHODS.forEach((methodName)=>{
            if (ee[methodName] === undefined) return;
            ee[methodName] = this._patchAddListener(ee, ee[methodName], context);
        });
        // patch methods that remove a listener
        if (typeof ee.removeListener === 'function') {
            ee.removeListener = this._patchRemoveListener(ee, ee.removeListener);
        }
        if (typeof ee.off === 'function') {
            ee.off = this._patchRemoveListener(ee, ee.off);
        }
        // patch method that remove all listeners
        if (typeof ee.removeAllListeners === 'function') {
            ee.removeAllListeners = this._patchRemoveAllListeners(ee, ee.removeAllListeners);
        }
        return ee;
    }
    /**
     * Patch methods that remove a given listener so that we match the "patched"
     * version of that listener (the one that propagate context).
     * @param ee EventEmitter instance
     * @param original reference to the patched method
     */ _patchRemoveListener(ee, original) {
        const contextManager = this;
        return function(event, listener) {
            var _a;
            const events = (_a = contextManager._getPatchMap(ee)) === null || _a === void 0 ? void 0 : _a[event];
            if (events === undefined) {
                return original.call(this, event, listener);
            }
            const patchedListener = events.get(listener);
            return original.call(this, event, patchedListener || listener);
        };
    }
    /**
     * Patch methods that remove all listeners so we remove our
     * internal references for a given event.
     * @param ee EventEmitter instance
     * @param original reference to the patched method
     */ _patchRemoveAllListeners(ee, original) {
        const contextManager = this;
        return function(event) {
            const map = contextManager._getPatchMap(ee);
            if (map !== undefined) {
                if (arguments.length === 0) {
                    contextManager._createPatchMap(ee);
                } else if (map[event] !== undefined) {
                    delete map[event];
                }
            }
            return original.apply(this, arguments);
        };
    }
    /**
     * Patch methods on an event emitter instance that can add listeners so we
     * can force them to propagate a given context.
     * @param ee EventEmitter instance
     * @param original reference to the patched method
     * @param [context] context to propagate when calling listeners
     */ _patchAddListener(ee, original, context) {
        const contextManager = this;
        return function(event, listener) {
            /**
             * This check is required to prevent double-wrapping the listener.
             * The implementation for ee.once wraps the listener and calls ee.on.
             * Without this check, we would wrap that wrapped listener.
             * This causes an issue because ee.removeListener depends on the onceWrapper
             * to properly remove the listener. If we wrap their wrapper, we break
             * that detection.
             */ if (contextManager._wrapped) {
                return original.call(this, event, listener);
            }
            let map = contextManager._getPatchMap(ee);
            if (map === undefined) {
                map = contextManager._createPatchMap(ee);
            }
            let listeners = map[event];
            if (listeners === undefined) {
                listeners = new WeakMap();
                map[event] = listeners;
            }
            const patchedListener = contextManager.bind(context, listener);
            // store a weak reference of the user listener to ours
            listeners.set(listener, patchedListener);
            /**
             * See comment at the start of this function for the explanation of this property.
             */ contextManager._wrapped = true;
            try {
                return original.call(this, event, patchedListener);
            } finally{
                contextManager._wrapped = false;
            }
        };
    }
    _createPatchMap(ee) {
        const map = Object.create(null);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ee[this._kOtListeners] = map;
        return map;
    }
    _getPatchMap(ee) {
        return ee[this._kOtListeners];
    }
}
exports.AbstractAsyncHooksContextManager = AbstractAsyncHooksContextManager; //# sourceMappingURL=AbstractAsyncHooksContextManager.js.map
}}),
"[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AsyncHooksContextManager = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const asyncHooks = __turbopack_context__.r("[externals]/async_hooks [external] (async_hooks, cjs)");
const AbstractAsyncHooksContextManager_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js [app-rsc] (ecmascript)");
class AsyncHooksContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {
    constructor(){
        super();
        this._contexts = new Map();
        this._stack = [];
        this._asyncHook = asyncHooks.createHook({
            init: this._init.bind(this),
            before: this._before.bind(this),
            after: this._after.bind(this),
            destroy: this._destroy.bind(this),
            promiseResolve: this._destroy.bind(this)
        });
    }
    active() {
        var _a;
        return (_a = this._stack[this._stack.length - 1]) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;
    }
    with(context, fn, thisArg, ...args) {
        this._enterContext(context);
        try {
            return fn.call(thisArg, ...args);
        } finally{
            this._exitContext();
        }
    }
    enable() {
        this._asyncHook.enable();
        return this;
    }
    disable() {
        this._asyncHook.disable();
        this._contexts.clear();
        this._stack = [];
        return this;
    }
    /**
     * Init hook will be called when userland create a async context, setting the
     * context as the current one if it exist.
     * @param uid id of the async context
     * @param type the resource type
     */ _init(uid, type) {
        // ignore TIMERWRAP as they combine timers with same timeout which can lead to
        // false context propagation. TIMERWRAP has been removed in node 11
        // every timer has it's own `Timeout` resource anyway which is used to propagate
        // context.
        if (type === 'TIMERWRAP') return;
        const context = this._stack[this._stack.length - 1];
        if (context !== undefined) {
            this._contexts.set(uid, context);
        }
    }
    /**
     * Destroy hook will be called when a given context is no longer used so we can
     * remove its attached context.
     * @param uid uid of the async context
     */ _destroy(uid) {
        this._contexts.delete(uid);
    }
    /**
     * Before hook is called just before executing a async context.
     * @param uid uid of the async context
     */ _before(uid) {
        const context = this._contexts.get(uid);
        if (context !== undefined) {
            this._enterContext(context);
        }
    }
    /**
     * After hook is called just after completing the execution of a async context.
     */ _after() {
        this._exitContext();
    }
    /**
     * Set the given context as active
     */ _enterContext(context) {
        this._stack.push(context);
    }
    /**
     * Remove the context at the root of the stack
     */ _exitContext() {
        this._stack.pop();
    }
}
exports.AsyncHooksContextManager = AsyncHooksContextManager; //# sourceMappingURL=AsyncHooksContextManager.js.map
}}),
"[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AsyncLocalStorageContextManager = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const async_hooks_1 = __turbopack_context__.r("[externals]/async_hooks [external] (async_hooks, cjs)");
const AbstractAsyncHooksContextManager_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AbstractAsyncHooksContextManager.js [app-rsc] (ecmascript)");
class AsyncLocalStorageContextManager extends AbstractAsyncHooksContextManager_1.AbstractAsyncHooksContextManager {
    constructor(){
        super();
        this._asyncLocalStorage = new async_hooks_1.AsyncLocalStorage();
    }
    active() {
        var _a;
        return (_a = this._asyncLocalStorage.getStore()) !== null && _a !== void 0 ? _a : api_1.ROOT_CONTEXT;
    }
    with(context, fn, thisArg, ...args) {
        const cb = thisArg == null ? fn : fn.bind(thisArg);
        return this._asyncLocalStorage.run(context, cb, ...args);
    }
    enable() {
        return this;
    }
    disable() {
        this._asyncLocalStorage.disable();
        return this;
    }
}
exports.AsyncLocalStorageContextManager = AsyncLocalStorageContextManager; //# sourceMappingURL=AsyncLocalStorageContextManager.js.map
}}),
"[project]/node_modules/@opentelemetry/context-async-hooks/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.AsyncLocalStorageContextManager = exports.AsyncHooksContextManager = void 0;
var AsyncHooksContextManager_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncHooksContextManager.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AsyncHooksContextManager", {
    enumerable: true,
    get: function() {
        return AsyncHooksContextManager_1.AsyncHooksContextManager;
    }
});
var AsyncLocalStorageContextManager_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/context-async-hooks/build/src/AsyncLocalStorageContextManager.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "AsyncLocalStorageContextManager", {
    enumerable: true,
    get: function() {
        return AsyncLocalStorageContextManager_1.AsyncLocalStorageContextManager;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/common.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "B3_DEBUG_FLAG_KEY": (()=>B3_DEBUG_FLAG_KEY)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
;
var B3_DEBUG_FLAG_KEY = (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["createContextKey"])('OpenTelemetry Context Key B3 Debug Flag'); //# sourceMappingURL=common.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/constants.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /** B3 single-header key */ __turbopack_context__.s({
    "B3_CONTEXT_HEADER": (()=>B3_CONTEXT_HEADER),
    "X_B3_FLAGS": (()=>X_B3_FLAGS),
    "X_B3_PARENT_SPAN_ID": (()=>X_B3_PARENT_SPAN_ID),
    "X_B3_SAMPLED": (()=>X_B3_SAMPLED),
    "X_B3_SPAN_ID": (()=>X_B3_SPAN_ID),
    "X_B3_TRACE_ID": (()=>X_B3_TRACE_ID)
});
var B3_CONTEXT_HEADER = 'b3';
var X_B3_TRACE_ID = 'x-b3-traceid';
var X_B3_SPAN_ID = 'x-b3-spanid';
var X_B3_SAMPLED = 'x-b3-sampled';
var X_B3_PARENT_SPAN_ID = 'x-b3-parentspanid';
var X_B3_FLAGS = 'x-b3-flags'; //# sourceMappingURL=constants.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3MultiPropagator.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "B3MultiPropagator": (()=>B3MultiPropagator)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/constants.js [app-rsc] (ecmascript)");
;
;
;
;
var VALID_SAMPLED_VALUES = new Set([
    true,
    'true',
    'True',
    '1',
    1
]);
var VALID_UNSAMPLED_VALUES = new Set([
    false,
    'false',
    'False',
    '0',
    0
]);
function isValidSampledValue(sampled) {
    return sampled === __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].SAMPLED || sampled === __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].NONE;
}
function parseHeader(header) {
    return Array.isArray(header) ? header[0] : header;
}
function getHeaderValue(carrier, getter, key) {
    var header = getter.get(carrier, key);
    return parseHeader(header);
}
function getTraceId(carrier, getter) {
    var traceId = getHeaderValue(carrier, getter, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_TRACE_ID"]);
    if (typeof traceId === 'string') {
        return traceId.padStart(32, '0');
    }
    return '';
}
function getSpanId(carrier, getter) {
    var spanId = getHeaderValue(carrier, getter, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SPAN_ID"]);
    if (typeof spanId === 'string') {
        return spanId;
    }
    return '';
}
function getDebug(carrier, getter) {
    var debug = getHeaderValue(carrier, getter, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_FLAGS"]);
    return debug === '1' ? '1' : undefined;
}
function getTraceFlags(carrier, getter) {
    var traceFlags = getHeaderValue(carrier, getter, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SAMPLED"]);
    var debug = getDebug(carrier, getter);
    if (debug === '1' || VALID_SAMPLED_VALUES.has(traceFlags)) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].SAMPLED;
    }
    if (traceFlags === undefined || VALID_UNSAMPLED_VALUES.has(traceFlags)) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].NONE;
    }
    // This indicates to isValidSampledValue that this is not valid
    return;
}
/**
 * Propagator for the B3 multiple-header HTTP format.
 * Based on: https://github.com/openzipkin/b3-propagation
 */ var B3MultiPropagator = function() {
    function B3MultiPropagator() {}
    B3MultiPropagator.prototype.inject = function(context, carrier, setter) {
        var spanContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getSpanContext(context);
        if (!spanContext || !(0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isSpanContextValid"])(spanContext) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTracingSuppressed"])(context)) return;
        var debug = context.getValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_DEBUG_FLAG_KEY"]);
        setter.set(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_TRACE_ID"], spanContext.traceId);
        setter.set(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SPAN_ID"], spanContext.spanId);
        // According to the B3 spec, if the debug flag is set,
        // the sampled flag shouldn't be propagated as well.
        if (debug === '1') {
            setter.set(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_FLAGS"], debug);
        } else if (spanContext.traceFlags !== undefined) {
            // We set the header only if there is an existing sampling decision.
            // Otherwise we will omit it => Absent.
            setter.set(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SAMPLED"], (__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].SAMPLED & spanContext.traceFlags) === __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].SAMPLED ? '1' : '0');
        }
    };
    B3MultiPropagator.prototype.extract = function(context, carrier, getter) {
        var traceId = getTraceId(carrier, getter);
        var spanId = getSpanId(carrier, getter);
        var traceFlags = getTraceFlags(carrier, getter);
        var debug = getDebug(carrier, getter);
        if ((0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isValidTraceId"])(traceId) && (0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isValidSpanId"])(spanId) && isValidSampledValue(traceFlags)) {
            context = context.setValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_DEBUG_FLAG_KEY"], debug);
            return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].setSpanContext(context, {
                traceId: traceId,
                spanId: spanId,
                isRemote: true,
                traceFlags: traceFlags
            });
        }
        return context;
    };
    B3MultiPropagator.prototype.fields = function() {
        return [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_TRACE_ID"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SPAN_ID"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_FLAGS"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SAMPLED"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_PARENT_SPAN_ID"]
        ];
    };
    return B3MultiPropagator;
}();
;
 //# sourceMappingURL=B3MultiPropagator.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3SinglePropagator.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "B3SinglePropagator": (()=>B3SinglePropagator)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/common.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/constants.js [app-rsc] (ecmascript)");
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
;
;
;
;
var B3_CONTEXT_REGEX = /((?:[0-9a-f]{16}){1,2})-([0-9a-f]{16})(?:-([01d](?![0-9a-f])))?(?:-([0-9a-f]{16}))?/;
var PADDING = '0'.repeat(16);
var SAMPLED_VALUES = new Set([
    'd',
    '1'
]);
var DEBUG_STATE = 'd';
function convertToTraceId128(traceId) {
    return traceId.length === 32 ? traceId : "" + PADDING + traceId;
}
function convertToTraceFlags(samplingState) {
    if (samplingState && SAMPLED_VALUES.has(samplingState)) {
        return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].SAMPLED;
    }
    return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].NONE;
}
/**
 * Propagator for the B3 single-header HTTP format.
 * Based on: https://github.com/openzipkin/b3-propagation
 */ var B3SinglePropagator = function() {
    function B3SinglePropagator() {}
    B3SinglePropagator.prototype.inject = function(context, carrier, setter) {
        var spanContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getSpanContext(context);
        if (!spanContext || !(0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isSpanContextValid"])(spanContext) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTracingSuppressed"])(context)) return;
        var samplingState = context.getValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_DEBUG_FLAG_KEY"]) || spanContext.traceFlags & 0x1;
        var value = spanContext.traceId + "-" + spanContext.spanId + "-" + samplingState;
        setter.set(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_CONTEXT_HEADER"], value);
    };
    B3SinglePropagator.prototype.extract = function(context, carrier, getter) {
        var header = getter.get(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_CONTEXT_HEADER"]);
        var b3Context = Array.isArray(header) ? header[0] : header;
        if (typeof b3Context !== 'string') return context;
        var match = b3Context.match(B3_CONTEXT_REGEX);
        if (!match) return context;
        var _a = __read(match, 4), extractedTraceId = _a[1], spanId = _a[2], samplingState = _a[3];
        var traceId = convertToTraceId128(extractedTraceId);
        if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isValidTraceId"])(traceId) || !(0, __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["isValidSpanId"])(spanId)) return context;
        var traceFlags = convertToTraceFlags(samplingState);
        if (samplingState === DEBUG_STATE) {
            context = context.setValue(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$common$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_DEBUG_FLAG_KEY"], samplingState);
        }
        return __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].setSpanContext(context, {
            traceId: traceId,
            spanId: spanId,
            isRemote: true,
            traceFlags: traceFlags
        });
    };
    B3SinglePropagator.prototype.fields = function() {
        return [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_CONTEXT_HEADER"]
        ];
    };
    return B3SinglePropagator;
}();
;
 //# sourceMappingURL=B3SinglePropagator.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/types.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /** Enumeration of B3 inject encodings */ __turbopack_context__.s({
    "B3InjectEncoding": (()=>B3InjectEncoding)
});
var B3InjectEncoding;
(function(B3InjectEncoding) {
    B3InjectEncoding[B3InjectEncoding["SINGLE_HEADER"] = 0] = "SINGLE_HEADER";
    B3InjectEncoding[B3InjectEncoding["MULTI_HEADER"] = 1] = "MULTI_HEADER";
})(B3InjectEncoding || (B3InjectEncoding = {})); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3Propagator.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "B3Propagator": (()=>B3Propagator)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3MultiPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3MultiPropagator.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3SinglePropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3SinglePropagator.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/constants.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/types.js [app-rsc] (ecmascript)");
;
;
;
;
;
/**
 * Propagator that extracts B3 context in both single and multi-header variants,
 * with configurable injection format defaulting to B3 single-header. Due to
 * the asymmetry in injection and extraction formats this is not suitable to
 * be implemented as a composite propagator.
 * Based on: https://github.com/openzipkin/b3-propagation
 */ var B3Propagator = function() {
    function B3Propagator(config) {
        if (config === void 0) {
            config = {};
        }
        this._b3MultiPropagator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3MultiPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3MultiPropagator"]();
        this._b3SinglePropagator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3SinglePropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3SinglePropagator"]();
        if (config.injectEncoding === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3InjectEncoding"].MULTI_HEADER) {
            this._inject = this._b3MultiPropagator.inject;
            this._fields = this._b3MultiPropagator.fields();
        } else {
            this._inject = this._b3SinglePropagator.inject;
            this._fields = this._b3SinglePropagator.fields();
        }
    }
    B3Propagator.prototype.inject = function(context, carrier, setter) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTracingSuppressed"])(context)) {
            return;
        }
        this._inject(context, carrier, setter);
    };
    B3Propagator.prototype.extract = function(context, carrier, getter) {
        var header = getter.get(carrier, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_CONTEXT_HEADER"]);
        var b3Context = Array.isArray(header) ? header[0] : header;
        if (b3Context) {
            return this._b3SinglePropagator.extract(context, carrier, getter);
        } else {
            return this._b3MultiPropagator.extract(context, carrier, getter);
        }
    };
    B3Propagator.prototype.fields = function() {
        return this._fields;
    };
    return B3Propagator;
}();
;
 //# sourceMappingURL=B3Propagator.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "B3InjectEncoding": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3InjectEncoding"]),
    "B3Propagator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3Propagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3Propagator"]),
    "B3_CONTEXT_HEADER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["B3_CONTEXT_HEADER"]),
    "X_B3_FLAGS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_FLAGS"]),
    "X_B3_PARENT_SPAN_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_PARENT_SPAN_ID"]),
    "X_B3_SAMPLED": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SAMPLED"]),
    "X_B3_SPAN_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_SPAN_ID"]),
    "X_B3_TRACE_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["X_B3_TRACE_ID"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$B3Propagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/B3Propagator.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/constants.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "B3InjectEncoding": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["B3InjectEncoding"]),
    "B3Propagator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["B3Propagator"]),
    "B3_CONTEXT_HEADER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["B3_CONTEXT_HEADER"]),
    "X_B3_FLAGS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["X_B3_FLAGS"]),
    "X_B3_PARENT_SPAN_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["X_B3_PARENT_SPAN_ID"]),
    "X_B3_SAMPLED": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["X_B3_SAMPLED"]),
    "X_B3_SPAN_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["X_B3_SPAN_ID"]),
    "X_B3_TRACE_ID": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["X_B3_TRACE_ID"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$b3$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/JaegerPropagator.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "JaegerPropagator": (()=>JaegerPropagator),
    "UBER_BAGGAGE_HEADER_PREFIX": (()=>UBER_BAGGAGE_HEADER_PREFIX),
    "UBER_TRACE_ID_HEADER": (()=>UBER_TRACE_ID_HEADER)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/trace/suppress-tracing.js [app-rsc] (ecmascript)");
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
;
;
var UBER_TRACE_ID_HEADER = 'uber-trace-id';
var UBER_BAGGAGE_HEADER_PREFIX = 'uberctx';
/**
 * Propagates {@link SpanContext} through Trace Context format propagation.
 * {trace-id}:{span-id}:{parent-span-id}:{flags}
 * {trace-id}
 * 64-bit or 128-bit random number in base16 format.
 * Can be variable length, shorter values are 0-padded on the left.
 * Value of 0 is invalid.
 * {span-id}
 * 64-bit random number in base16 format.
 * {parent-span-id}
 * Set to 0 because this field is deprecated.
 * {flags}
 * One byte bitmap, as two hex digits.
 * Inspired by jaeger-client-node project.
 */ var JaegerPropagator = function() {
    function JaegerPropagator(config) {
        if (typeof config === 'string') {
            this._jaegerTraceHeader = config;
            this._jaegerBaggageHeaderPrefix = UBER_BAGGAGE_HEADER_PREFIX;
        } else {
            this._jaegerTraceHeader = (config === null || config === void 0 ? void 0 : config.customTraceHeader) || UBER_TRACE_ID_HEADER;
            this._jaegerBaggageHeaderPrefix = (config === null || config === void 0 ? void 0 : config.customBaggageHeaderPrefix) || UBER_BAGGAGE_HEADER_PREFIX;
        }
    }
    JaegerPropagator.prototype.inject = function(context, carrier, setter) {
        var e_1, _a;
        var spanContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getSpanContext(context);
        var baggage = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["propagation"].getBaggage(context);
        if (spanContext && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$trace$2f$suppress$2d$tracing$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTracingSuppressed"])(context) === false) {
            var traceFlags = "0" + (spanContext.traceFlags || __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["TraceFlags"].NONE).toString(16);
            setter.set(carrier, this._jaegerTraceHeader, spanContext.traceId + ":" + spanContext.spanId + ":0:" + traceFlags);
        }
        if (baggage) {
            try {
                for(var _b = __values(baggage.getAllEntries()), _c = _b.next(); !_c.done; _c = _b.next()){
                    var _d = __read(_c.value, 2), key = _d[0], entry = _d[1];
                    setter.set(carrier, this._jaegerBaggageHeaderPrefix + "-" + key, encodeURIComponent(entry.value));
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        }
    };
    JaegerPropagator.prototype.extract = function(context, carrier, getter) {
        var e_2, _a;
        var _this = this;
        var _b;
        var uberTraceIdHeader = getter.get(carrier, this._jaegerTraceHeader);
        var uberTraceId = Array.isArray(uberTraceIdHeader) ? uberTraceIdHeader[0] : uberTraceIdHeader;
        var baggageValues = getter.keys(carrier).filter(function(key) {
            return key.startsWith(_this._jaegerBaggageHeaderPrefix + "-");
        }).map(function(key) {
            var value = getter.get(carrier, key);
            return {
                key: key.substring(_this._jaegerBaggageHeaderPrefix.length + 1),
                value: Array.isArray(value) ? value[0] : value
            };
        });
        var newContext = context;
        // if the trace id header is present and valid, inject it into the context
        if (typeof uberTraceId === 'string') {
            var spanContext = deserializeSpanContext(uberTraceId);
            if (spanContext) {
                newContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].setSpanContext(newContext, spanContext);
            }
        }
        if (baggageValues.length === 0) return newContext;
        // if baggage values are present, inject it into the current baggage
        var currentBaggage = (_b = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["propagation"].getBaggage(context)) !== null && _b !== void 0 ? _b : __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["propagation"].createBaggage();
        try {
            for(var baggageValues_1 = __values(baggageValues), baggageValues_1_1 = baggageValues_1.next(); !baggageValues_1_1.done; baggageValues_1_1 = baggageValues_1.next()){
                var baggageEntry = baggageValues_1_1.value;
                if (baggageEntry.value === undefined) continue;
                currentBaggage = currentBaggage.setEntry(baggageEntry.key, {
                    value: decodeURIComponent(baggageEntry.value)
                });
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (baggageValues_1_1 && !baggageValues_1_1.done && (_a = baggageValues_1.return)) _a.call(baggageValues_1);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        newContext = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["propagation"].setBaggage(newContext, currentBaggage);
        return newContext;
    };
    JaegerPropagator.prototype.fields = function() {
        return [
            this._jaegerTraceHeader
        ];
    };
    return JaegerPropagator;
}();
;
var VALID_HEX_RE = /^[0-9a-f]{1,2}$/i;
/**
 * @param {string} serializedString - a serialized span context.
 * @return {SpanContext} - returns a span context represented by the serializedString.
 **/ function deserializeSpanContext(serializedString) {
    var headers = decodeURIComponent(serializedString).split(':');
    if (headers.length !== 4) {
        return null;
    }
    var _a = __read(headers, 4), _traceId = _a[0], _spanId = _a[1], flags = _a[3];
    var traceId = _traceId.padStart(32, '0');
    var spanId = _spanId.padStart(16, '0');
    var traceFlags = VALID_HEX_RE.test(flags) ? parseInt(flags, 16) & 1 : 1;
    return {
        traceId: traceId,
        spanId: spanId,
        isRemote: true,
        traceFlags: traceFlags
    };
} //# sourceMappingURL=JaegerPropagator.js.map
}}),
"[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "JaegerPropagator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$JaegerPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JaegerPropagator"]),
    "UBER_BAGGAGE_HEADER_PREFIX": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$JaegerPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UBER_BAGGAGE_HEADER_PREFIX"]),
    "UBER_TRACE_ID_HEADER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$JaegerPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UBER_TRACE_ID_HEADER"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$JaegerPropagator$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/JaegerPropagator.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "JaegerPropagator": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["JaegerPropagator"]),
    "UBER_BAGGAGE_HEADER_PREFIX": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["UBER_BAGGAGE_HEADER_PREFIX"]),
    "UBER_TRACE_ID_HEADER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["UBER_TRACE_ID_HEADER"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$propagator$2d$jaeger$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/NodeTracerProvider.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.NodeTracerProvider = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ const context_async_hooks_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/context-async-hooks/build/src/index.js [app-rsc] (ecmascript)");
const propagator_b3_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/propagator-b3/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_trace_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)");
const semver = __turbopack_context__.r("[project]/node_modules/semver/index.js [app-rsc] (ecmascript)");
const propagator_jaeger_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/propagator-jaeger/build/esm/index.js [app-rsc] (ecmascript)");
/**
 * Register this TracerProvider for use with the OpenTelemetry API.
 * Undefined values may be replaced with defaults, and
 * null values will be skipped.
 *
 * @param config Configuration object for SDK registration
 */ class NodeTracerProvider extends sdk_trace_base_1.BasicTracerProvider {
    constructor(config = {}){
        super(config);
    }
    register(config = {}) {
        if (config.contextManager === undefined) {
            const ContextManager = semver.gte(process.version, '14.8.0') ? context_async_hooks_1.AsyncLocalStorageContextManager : context_async_hooks_1.AsyncHooksContextManager;
            config.contextManager = new ContextManager();
            config.contextManager.enable();
        }
        super.register(config);
    }
}
exports.NodeTracerProvider = NodeTracerProvider;
NodeTracerProvider._registeredPropagators = new Map([
    ...sdk_trace_base_1.BasicTracerProvider._registeredPropagators,
    [
        'b3',
        ()=>new propagator_b3_1.B3Propagator({
                injectEncoding: propagator_b3_1.B3InjectEncoding.SINGLE_HEADER
            })
    ],
    [
        'b3multi',
        ()=>new propagator_b3_1.B3Propagator({
                injectEncoding: propagator_b3_1.B3InjectEncoding.MULTI_HEADER
            })
    ],
    [
        'jaeger',
        ()=>new propagator_jaeger_1.JaegerPropagator()
    ]
]); //# sourceMappingURL=NodeTracerProvider.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
__exportStar(__turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/NodeTracerProvider.js [app-rsc] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)"), exports); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
;
;
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * Enable instrumentations
 * @param instrumentations
 * @param tracerProvider
 * @param meterProvider
 */ __turbopack_context__.s({
    "disableInstrumentations": (()=>disableInstrumentations),
    "enableInstrumentations": (()=>enableInstrumentations)
});
function enableInstrumentations(instrumentations, tracerProvider, meterProvider, loggerProvider) {
    for(var i = 0, j = instrumentations.length; i < j; i++){
        var instrumentation = instrumentations[i];
        if (tracerProvider) {
            instrumentation.setTracerProvider(tracerProvider);
        }
        if (meterProvider) {
            instrumentation.setMeterProvider(meterProvider);
        }
        if (loggerProvider && instrumentation.setLoggerProvider) {
            instrumentation.setLoggerProvider(loggerProvider);
        }
        // instrumentations have been already enabled during creation
        // so enable only if user prevented that by setting enabled to false
        // this is to prevent double enabling but when calling register all
        // instrumentations should be now enabled
        if (!instrumentation.getConfig().enabled) {
            instrumentation.enable();
        }
    }
}
function disableInstrumentations(instrumentations) {
    instrumentations.forEach(function(instrumentation) {
        return instrumentation.disable();
    });
} //# sourceMappingURL=autoLoaderUtils.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "registerInstrumentations": (()=>registerInstrumentations)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$autoLoaderUtils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/autoLoaderUtils.js [app-rsc] (ecmascript)");
;
;
;
function registerInstrumentations(options) {
    var _a, _b;
    var tracerProvider = options.tracerProvider || __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getTracerProvider();
    var meterProvider = options.meterProvider || __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["metrics"].getMeterProvider();
    var loggerProvider = options.loggerProvider || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["logs"].getLoggerProvider();
    var instrumentations = (_b = (_a = options.instrumentations) === null || _a === void 0 ? void 0 : _a.flat()) !== null && _b !== void 0 ? _b : [];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$autoLoaderUtils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["enableInstrumentations"])(instrumentations, tracerProvider, meterProvider, loggerProvider);
    return function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$autoLoaderUtils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["disableInstrumentations"])(instrumentations);
    };
} //# sourceMappingURL=autoLoader.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "InstrumentationAbstract": (()=>InstrumentationAbstract)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/shimmer/index.js [app-rsc] (ecmascript)");
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
/**
 * Base abstract internal class for instrumenting node and web plugins
 */ var InstrumentationAbstract = function() {
    function InstrumentationAbstract(instrumentationName, instrumentationVersion, config) {
        this.instrumentationName = instrumentationName;
        this.instrumentationVersion = instrumentationVersion;
        /* Api to wrap instrumented method */ this._wrap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["wrap"];
        /* Api to unwrap instrumented methods */ this._unwrap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"];
        /* Api to mass wrap instrumented method */ this._massWrap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["massWrap"];
        /* Api to mass unwrap instrumented methods */ this._massUnwrap = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["massUnwrap"];
        // copy config first level properties to ensure they are immutable.
        // nested properties are not copied, thus are mutable from the outside.
        this._config = __assign({
            enabled: true
        }, config);
        this._diag = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].createComponentLogger({
            namespace: instrumentationName
        });
        this._tracer = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["trace"].getTracer(instrumentationName, instrumentationVersion);
        this._meter = __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["metrics"].getMeter(instrumentationName, instrumentationVersion);
        this._logger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$api$2d$logs$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["logs"].getLogger(instrumentationName, instrumentationVersion);
        this._updateMetricInstruments();
    }
    Object.defineProperty(InstrumentationAbstract.prototype, "meter", {
        /* Returns meter */ get: function() {
            return this._meter;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Sets MeterProvider to this plugin
     * @param meterProvider
     */ InstrumentationAbstract.prototype.setMeterProvider = function(meterProvider) {
        this._meter = meterProvider.getMeter(this.instrumentationName, this.instrumentationVersion);
        this._updateMetricInstruments();
    };
    Object.defineProperty(InstrumentationAbstract.prototype, "logger", {
        /* Returns logger */ get: function() {
            return this._logger;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Sets LoggerProvider to this plugin
     * @param loggerProvider
     */ InstrumentationAbstract.prototype.setLoggerProvider = function(loggerProvider) {
        this._logger = loggerProvider.getLogger(this.instrumentationName, this.instrumentationVersion);
    };
    /**
     * @experimental
     *
     * Get module definitions defined by {@link init}.
     * This can be used for experimental compile-time instrumentation.
     *
     * @returns an array of {@link InstrumentationModuleDefinition}
     */ InstrumentationAbstract.prototype.getModuleDefinitions = function() {
        var _a;
        var initResult = (_a = this.init()) !== null && _a !== void 0 ? _a : [];
        if (!Array.isArray(initResult)) {
            return [
                initResult
            ];
        }
        return initResult;
    };
    /**
     * Sets the new metric instruments with the current Meter.
     */ InstrumentationAbstract.prototype._updateMetricInstruments = function() {
        return;
    };
    /* Returns InstrumentationConfig */ InstrumentationAbstract.prototype.getConfig = function() {
        return this._config;
    };
    /**
     * Sets InstrumentationConfig to this plugin
     * @param InstrumentationConfig
     */ InstrumentationAbstract.prototype.setConfig = function(config) {
        // copy config first level properties to ensure they are immutable.
        // nested properties are not copied, thus are mutable from the outside.
        this._config = __assign({}, config);
    };
    /**
     * Sets TraceProvider to this plugin
     * @param tracerProvider
     */ InstrumentationAbstract.prototype.setTracerProvider = function(tracerProvider) {
        this._tracer = tracerProvider.getTracer(this.instrumentationName, this.instrumentationVersion);
    };
    Object.defineProperty(InstrumentationAbstract.prototype, "tracer", {
        /* Returns tracer */ get: function() {
            return this._tracer;
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Execute span customization hook, if configured, and log any errors.
     * Any semantics of the trigger and info are defined by the specific instrumentation.
     * @param hookHandler The optional hook handler which the user has configured via instrumentation config
     * @param triggerName The name of the trigger for executing the hook for logging purposes
     * @param span The span to which the hook should be applied
     * @param info The info object to be passed to the hook, with useful data the hook may use
     */ InstrumentationAbstract.prototype._runSpanCustomizationHook = function(hookHandler, triggerName, span, info) {
        if (!hookHandler) {
            return;
        }
        try {
            hookHandler(span, info);
        } catch (e) {
            this._diag.error("Error running span customization hook due to exception in handler", {
                triggerName: triggerName
            }, e);
        }
    };
    return InstrumentationAbstract;
}();
;
 //# sourceMappingURL=instrumentation.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ModuleNameSeparator": (()=>ModuleNameSeparator),
    "ModuleNameTrie": (()=>ModuleNameTrie)
});
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
var __spreadArray = this && this.__spreadArray || function(to, from, pack) {
    if (pack || arguments.length === 2) for(var i = 0, l = from.length, ar; i < l; i++){
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var ModuleNameSeparator = '/';
/**
 * Node in a `ModuleNameTrie`
 */ var ModuleNameTrieNode = function() {
    function ModuleNameTrieNode() {
        this.hooks = [];
        this.children = new Map();
    }
    return ModuleNameTrieNode;
}();
/**
 * Trie containing nodes that represent a part of a module name (i.e. the parts separated by forward slash)
 */ var ModuleNameTrie = function() {
    function ModuleNameTrie() {
        this._trie = new ModuleNameTrieNode();
        this._counter = 0;
    }
    /**
     * Insert a module hook into the trie
     *
     * @param {Hooked} hook Hook
     */ ModuleNameTrie.prototype.insert = function(hook) {
        var e_1, _a;
        var trieNode = this._trie;
        try {
            for(var _b = __values(hook.moduleName.split(ModuleNameSeparator)), _c = _b.next(); !_c.done; _c = _b.next()){
                var moduleNamePart = _c.value;
                var nextNode = trieNode.children.get(moduleNamePart);
                if (!nextNode) {
                    nextNode = new ModuleNameTrieNode();
                    trieNode.children.set(moduleNamePart, nextNode);
                }
                trieNode = nextNode;
            }
        } catch (e_1_1) {
            e_1 = {
                error: e_1_1
            };
        } finally{
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            } finally{
                if (e_1) throw e_1.error;
            }
        }
        trieNode.hooks.push({
            hook: hook,
            insertedId: this._counter++
        });
    };
    /**
     * Search for matching hooks in the trie
     *
     * @param {string} moduleName Module name
     * @param {boolean} maintainInsertionOrder Whether to return the results in insertion order
     * @param {boolean} fullOnly Whether to return only full matches
     * @returns {Hooked[]} Matching hooks
     */ ModuleNameTrie.prototype.search = function(moduleName, _a) {
        var e_2, _b;
        var _c = _a === void 0 ? {} : _a, maintainInsertionOrder = _c.maintainInsertionOrder, fullOnly = _c.fullOnly;
        var trieNode = this._trie;
        var results = [];
        var foundFull = true;
        try {
            for(var _d = __values(moduleName.split(ModuleNameSeparator)), _e = _d.next(); !_e.done; _e = _d.next()){
                var moduleNamePart = _e.value;
                var nextNode = trieNode.children.get(moduleNamePart);
                if (!nextNode) {
                    foundFull = false;
                    break;
                }
                if (!fullOnly) {
                    results.push.apply(results, __spreadArray([], __read(nextNode.hooks), false));
                }
                trieNode = nextNode;
            }
        } catch (e_2_1) {
            e_2 = {
                error: e_2_1
            };
        } finally{
            try {
                if (_e && !_e.done && (_b = _d.return)) _b.call(_d);
            } finally{
                if (e_2) throw e_2.error;
            }
        }
        if (fullOnly && foundFull) {
            results.push.apply(results, __spreadArray([], __read(trieNode.hooks), false));
        }
        if (results.length === 0) {
            return [];
        }
        if (results.length === 1) {
            return [
                results[0].hook
            ];
        }
        if (maintainInsertionOrder) {
            results.sort(function(a, b) {
                return a.insertedId - b.insertedId;
            });
        }
        return results.map(function(_a) {
            var hook = _a.hook;
            return hook;
        });
    };
    return ModuleNameTrie;
}();
;
 //# sourceMappingURL=ModuleNameTrie.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "RequireInTheMiddleSingleton": (()=>RequireInTheMiddleSingleton)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$require$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$require$2d$in$2d$the$2d$middle$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/require-in-the-middle [external] (require-in-the-middle, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$ModuleNameTrie$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ModuleNameTrie.js [app-rsc] (ecmascript)");
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
/**
 * Whether Mocha is running in this process
 * Inspired by https://github.com/AndreasPizsa/detect-mocha
 *
 * @type {boolean}
 */ var isMocha = [
    'afterEach',
    'after',
    'beforeEach',
    'before',
    'describe',
    'it'
].every(function(fn) {
    // @ts-expect-error TS7053: Element implicitly has an 'any' type
    return typeof global[fn] === 'function';
});
/**
 * Singleton class for `require-in-the-middle`
 * Allows instrumentation plugins to patch modules with only a single `require` patch
 * WARNING: Because this class will create its own `require-in-the-middle` (RITM) instance,
 * we should minimize the number of new instances of this class.
 * Multiple instances of `@opentelemetry/instrumentation` (e.g. multiple versions) in a single process
 * will result in multiple instances of RITM, which will have an impact
 * on the performance of instrumentation hooks being applied.
 */ var RequireInTheMiddleSingleton = function() {
    function RequireInTheMiddleSingleton() {
        this._moduleNameTrie = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$ModuleNameTrie$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ModuleNameTrie"]();
        this._initialize();
    }
    RequireInTheMiddleSingleton.prototype._initialize = function() {
        var _this = this;
        new __TURBOPACK__imported__module__$5b$externals$5d2f$require$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$require$2d$in$2d$the$2d$middle$2c$__cjs$29$__["Hook"](// Intercept all `require` calls; we will filter the matching ones below
        null, {
            internals: true
        }, function(exports, name, basedir) {
            var e_1, _a;
            // For internal files on Windows, `name` will use backslash as the path separator
            var normalizedModuleName = normalizePathSeparators(name);
            var matches = _this._moduleNameTrie.search(normalizedModuleName, {
                maintainInsertionOrder: true,
                // For core modules (e.g. `fs`), do not match on sub-paths (e.g. `fs/promises').
                // This matches the behavior of `require-in-the-middle`.
                // `basedir` is always `undefined` for core modules.
                fullOnly: basedir === undefined
            });
            try {
                for(var matches_1 = __values(matches), matches_1_1 = matches_1.next(); !matches_1_1.done; matches_1_1 = matches_1.next()){
                    var onRequire = matches_1_1.value.onRequire;
                    exports = onRequire(exports, name, basedir);
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (matches_1_1 && !matches_1_1.done && (_a = matches_1.return)) _a.call(matches_1);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            return exports;
        });
    };
    /**
     * Register a hook with `require-in-the-middle`
     *
     * @param {string} moduleName Module name
     * @param {OnRequireFn} onRequire Hook function
     * @returns {Hooked} Registered hook
     */ RequireInTheMiddleSingleton.prototype.register = function(moduleName, onRequire) {
        var hooked = {
            moduleName: moduleName,
            onRequire: onRequire
        };
        this._moduleNameTrie.insert(hooked);
        return hooked;
    };
    /**
     * Get the `RequireInTheMiddleSingleton` singleton
     *
     * @returns {RequireInTheMiddleSingleton} Singleton of `RequireInTheMiddleSingleton`
     */ RequireInTheMiddleSingleton.getInstance = function() {
        var _a;
        // Mocha runs all test suites in the same process
        // This prevents test suites from sharing a singleton
        if (isMocha) return new RequireInTheMiddleSingleton();
        return this._instance = (_a = this._instance) !== null && _a !== void 0 ? _a : new RequireInTheMiddleSingleton();
    };
    return RequireInTheMiddleSingleton;
}();
;
/**
 * Normalize the path separators to forward slash in a module name or path
 *
 * @param {string} moduleNameOrPath Module name or path
 * @returns {string} Normalized module name or path
 */ function normalizePathSeparators(moduleNameOrPath) {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["sep"] !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$ModuleNameTrie$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ModuleNameSeparator"] ? moduleNameOrPath.split(__TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["sep"]).join(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$ModuleNameTrie$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ModuleNameSeparator"]) : moduleNameOrPath;
} //# sourceMappingURL=RequireInTheMiddleSingleton.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "isWrapped": (()=>isWrapped),
    "safeExecuteInTheMiddle": (()=>safeExecuteInTheMiddle),
    "safeExecuteInTheMiddleAsync": (()=>safeExecuteInTheMiddleAsync)
});
var __awaiter = this && this.__awaiter || function(thisArg, _arguments, P, generator) {
    function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
            resolve(value);
        });
    }
    return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        }
        function rejected(value) {
            try {
                step(generator["throw"](value));
            } catch (e) {
                reject(e);
            }
        }
        function step(result) {
            result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = this && this.__generator || function(thisArg, body) {
    var _ = {
        label: 0,
        sent: function() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    "TURBOPACK unreachable";
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(_)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
};
function safeExecuteInTheMiddle(execute, onFinish, preventThrowingError) {
    var error;
    var result;
    try {
        result = execute();
    } catch (e) {
        error = e;
    } finally{
        onFinish(error, result);
        if (error && !preventThrowingError) {
            // eslint-disable-next-line no-unsafe-finally
            throw error;
        }
        // eslint-disable-next-line no-unsafe-finally
        return result;
    }
}
function safeExecuteInTheMiddleAsync(execute, onFinish, preventThrowingError) {
    return __awaiter(this, void 0, void 0, function() {
        var error, result, e_1;
        return __generator(this, function(_a) {
            switch(_a.label){
                case 0:
                    _a.trys.push([
                        0,
                        2,
                        3,
                        4
                    ]);
                    return [
                        4 /*yield*/ ,
                        execute()
                    ];
                case 1:
                    result = _a.sent();
                    return [
                        3 /*break*/ ,
                        4
                    ];
                case 2:
                    e_1 = _a.sent();
                    error = e_1;
                    return [
                        3 /*break*/ ,
                        4
                    ];
                case 3:
                    onFinish(error, result);
                    if (error && !preventThrowingError) {
                        // eslint-disable-next-line no-unsafe-finally
                        throw error;
                    }
                    // eslint-disable-next-line no-unsafe-finally
                    return [
                        2 /*return*/ ,
                        result
                    ];
                case 4:
                    return [
                        2 /*return*/ 
                    ];
            }
        });
    });
}
function isWrapped(func) {
    return typeof func === 'function' && typeof func.__original === 'function' && typeof func.__unwrap === 'function' && func.__wrapped === true;
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "InstrumentationBase": (()=>InstrumentationBase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/util [external] (util, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$semver$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/semver/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/shimmer/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$RequireInTheMiddleSingleton$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/RequireInTheMiddleSingleton.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$import$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$import$2d$in$2d$the$2d$middle$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/import-in-the-middle [external] (import-in-the-middle, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$require$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$require$2d$in$2d$the$2d$middle$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/require-in-the-middle [external] (require-in-the-middle, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/utils.js [app-rsc] (ecmascript)");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
;
;
;
;
;
;
;
;
;
;
;
/**
 * Base abstract class for instrumenting node plugins
 */ var InstrumentationBase = function(_super) {
    __extends(InstrumentationBase, _super);
    function InstrumentationBase(instrumentationName, instrumentationVersion, config) {
        var _this = _super.call(this, instrumentationName, instrumentationVersion, config) || this;
        _this._hooks = [];
        _this._requireInTheMiddleSingleton = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$RequireInTheMiddleSingleton$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["RequireInTheMiddleSingleton"].getInstance();
        _this._enabled = false;
        _this._wrap = function(moduleExports, name, wrapper) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isWrapped"])(moduleExports[name])) {
                _this._unwrap(moduleExports, name);
            }
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["types"].isProxy(moduleExports)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["wrap"])(moduleExports, name, wrapper);
            } else {
                var wrapped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["wrap"])(Object.assign({}, moduleExports), name, wrapper);
                return Object.defineProperty(moduleExports, name, {
                    value: wrapped
                });
            }
        };
        _this._unwrap = function(moduleExports, name) {
            if (!__TURBOPACK__imported__module__$5b$externals$5d2f$util__$5b$external$5d$__$28$util$2c$__cjs$29$__["types"].isProxy(moduleExports)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$shimmer$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unwrap"])(moduleExports, name);
            } else {
                return Object.defineProperty(moduleExports, name, {
                    value: moduleExports[name]
                });
            }
        };
        _this._massWrap = function(moduleExportsArray, names, wrapper) {
            if (!moduleExportsArray) {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error('must provide one or more modules to patch');
                return;
            } else if (!Array.isArray(moduleExportsArray)) {
                moduleExportsArray = [
                    moduleExportsArray
                ];
            }
            if (!(names && Array.isArray(names))) {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error('must provide one or more functions to wrap on modules');
                return;
            }
            moduleExportsArray.forEach(function(moduleExports) {
                names.forEach(function(name) {
                    _this._wrap(moduleExports, name, wrapper);
                });
            });
        };
        _this._massUnwrap = function(moduleExportsArray, names) {
            if (!moduleExportsArray) {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error('must provide one or more modules to patch');
                return;
            } else if (!Array.isArray(moduleExportsArray)) {
                moduleExportsArray = [
                    moduleExportsArray
                ];
            }
            if (!(names && Array.isArray(names))) {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error('must provide one or more functions to wrap on modules');
                return;
            }
            moduleExportsArray.forEach(function(moduleExports) {
                names.forEach(function(name) {
                    _this._unwrap(moduleExports, name);
                });
            });
        };
        var modules = _this.init();
        if (modules && !Array.isArray(modules)) {
            modules = [
                modules
            ];
        }
        _this._modules = modules || [];
        if (_this._modules.length === 0) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('No modules instrumentation has been defined for ' + ("'" + _this.instrumentationName + "@" + _this.instrumentationVersion + "'") + ', nothing will be patched');
        }
        if (_this._config.enabled) {
            _this.enable();
        }
        return _this;
    }
    InstrumentationBase.prototype._warnOnPreloadedModules = function() {
        var _this = this;
        this._modules.forEach(function(module) {
            var name = module.name;
            try {
                var resolvedModule = (()=>{
                    const e = new Error("Cannot find module as expression is too dynamic");
                    e.code = 'MODULE_NOT_FOUND';
                    throw e;
                })();
                if (__turbopack_context__.c[resolvedModule]) {
                    // Module is already cached, which means the instrumentation hook might not work
                    _this._diag.warn("Module " + name + " has been loaded before " + _this.instrumentationName + " so it might not work, please initialize it before requiring " + name);
                }
            } catch (_a) {
            // Module isn't available, we can simply skip
            }
        });
    };
    InstrumentationBase.prototype._extractPackageVersion = function(baseDir) {
        try {
            var json = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["readFileSync"])((0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(baseDir, 'package.json'), {
                encoding: 'utf8'
            });
            var version = JSON.parse(json).version;
            return typeof version === 'string' ? version : undefined;
        } catch (error) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Failed extracting version', baseDir);
        }
        return undefined;
    };
    InstrumentationBase.prototype._onRequire = function(module, exports, name, baseDir) {
        var _this = this;
        var _a;
        if (!baseDir) {
            if (typeof module.patch === 'function') {
                module.moduleExports = exports;
                if (this._enabled) {
                    this._diag.debug('Applying instrumentation patch for nodejs core module on require hook', {
                        module: module.name
                    });
                    return module.patch(exports);
                }
            }
            return exports;
        }
        var version = this._extractPackageVersion(baseDir);
        module.moduleVersion = version;
        if (module.name === name) {
            // main module
            if (isSupported(module.supportedVersions, version, module.includePrerelease)) {
                if (typeof module.patch === 'function') {
                    module.moduleExports = exports;
                    if (this._enabled) {
                        this._diag.debug('Applying instrumentation patch for module on require hook', {
                            module: module.name,
                            version: module.moduleVersion,
                            baseDir: baseDir
                        });
                        return module.patch(exports, module.moduleVersion);
                    }
                }
            }
            return exports;
        }
        // internal file
        var files = (_a = module.files) !== null && _a !== void 0 ? _a : [];
        var normalizedName = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["normalize"])(name);
        var supportedFileInstrumentations = files.filter(function(f) {
            return f.name === normalizedName;
        }).filter(function(f) {
            return isSupported(f.supportedVersions, version, module.includePrerelease);
        });
        return supportedFileInstrumentations.reduce(function(patchedExports, file) {
            file.moduleExports = patchedExports;
            if (_this._enabled) {
                _this._diag.debug('Applying instrumentation patch for nodejs module file on require hook', {
                    module: module.name,
                    version: module.moduleVersion,
                    fileName: file.name,
                    baseDir: baseDir
                });
                // patch signature is not typed, so we cast it assuming it's correct
                return file.patch(patchedExports, module.moduleVersion);
            }
            return patchedExports;
        }, exports);
    };
    InstrumentationBase.prototype.enable = function() {
        var e_1, _a, e_2, _b, e_3, _c;
        var _this = this;
        if (this._enabled) {
            return;
        }
        this._enabled = true;
        // already hooked, just call patch again
        if (this._hooks.length > 0) {
            try {
                for(var _d = __values(this._modules), _e = _d.next(); !_e.done; _e = _d.next()){
                    var module_1 = _e.value;
                    if (typeof module_1.patch === 'function' && module_1.moduleExports) {
                        this._diag.debug('Applying instrumentation patch for nodejs module on instrumentation enabled', {
                            module: module_1.name,
                            version: module_1.moduleVersion
                        });
                        module_1.patch(module_1.moduleExports, module_1.moduleVersion);
                    }
                    try {
                        for(var _f = (e_2 = void 0, __values(module_1.files)), _g = _f.next(); !_g.done; _g = _f.next()){
                            var file = _g.value;
                            if (file.moduleExports) {
                                this._diag.debug('Applying instrumentation patch for nodejs module file on instrumentation enabled', {
                                    module: module_1.name,
                                    version: module_1.moduleVersion,
                                    fileName: file.name
                                });
                                file.patch(file.moduleExports, module_1.moduleVersion);
                            }
                        }
                    } catch (e_2_1) {
                        e_2 = {
                            error: e_2_1
                        };
                    } finally{
                        try {
                            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);
                        } finally{
                            if (e_2) throw e_2.error;
                        }
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (_e && !_e.done && (_a = _d.return)) _a.call(_d);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
            return;
        }
        this._warnOnPreloadedModules();
        var _loop_1 = function(module_2) {
            var hookFn = function(exports, name, baseDir) {
                return _this._onRequire(module_2, exports, name, baseDir);
            };
            var onRequire = function(exports, name, baseDir) {
                return _this._onRequire(module_2, exports, name, baseDir);
            };
            // `RequireInTheMiddleSingleton` does not support absolute paths.
            // For an absolute paths, we must create a separate instance of the
            // require-in-the-middle `Hook`.
            var hook = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["isAbsolute"])(module_2.name) ? new __TURBOPACK__imported__module__$5b$externals$5d2f$require$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$require$2d$in$2d$the$2d$middle$2c$__cjs$29$__["Hook"]([
                module_2.name
            ], {
                internals: true
            }, onRequire) : this_1._requireInTheMiddleSingleton.register(module_2.name, onRequire);
            this_1._hooks.push(hook);
            var esmHook = new __TURBOPACK__imported__module__$5b$externals$5d2f$import$2d$in$2d$the$2d$middle__$5b$external$5d$__$28$import$2d$in$2d$the$2d$middle$2c$__cjs$29$__["Hook"]([
                module_2.name
            ], {
                internals: false
            }, hookFn);
            this_1._hooks.push(esmHook);
        };
        var this_1 = this;
        try {
            for(var _h = __values(this._modules), _j = _h.next(); !_j.done; _j = _h.next()){
                var module_2 = _j.value;
                _loop_1(module_2);
            }
        } catch (e_3_1) {
            e_3 = {
                error: e_3_1
            };
        } finally{
            try {
                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);
            } finally{
                if (e_3) throw e_3.error;
            }
        }
    };
    InstrumentationBase.prototype.disable = function() {
        var e_4, _a, e_5, _b;
        if (!this._enabled) {
            return;
        }
        this._enabled = false;
        try {
            for(var _c = __values(this._modules), _d = _c.next(); !_d.done; _d = _c.next()){
                var module_3 = _d.value;
                if (typeof module_3.unpatch === 'function' && module_3.moduleExports) {
                    this._diag.debug('Removing instrumentation patch for nodejs module on instrumentation disabled', {
                        module: module_3.name,
                        version: module_3.moduleVersion
                    });
                    module_3.unpatch(module_3.moduleExports, module_3.moduleVersion);
                }
                try {
                    for(var _e = (e_5 = void 0, __values(module_3.files)), _f = _e.next(); !_f.done; _f = _e.next()){
                        var file = _f.value;
                        if (file.moduleExports) {
                            this._diag.debug('Removing instrumentation patch for nodejs module file on instrumentation disabled', {
                                module: module_3.name,
                                version: module_3.moduleVersion,
                                fileName: file.name
                            });
                            file.unpatch(file.moduleExports, module_3.moduleVersion);
                        }
                    }
                } catch (e_5_1) {
                    e_5 = {
                        error: e_5_1
                    };
                } finally{
                    try {
                        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);
                    } finally{
                        if (e_5) throw e_5.error;
                    }
                }
            }
        } catch (e_4_1) {
            e_4 = {
                error: e_4_1
            };
        } finally{
            try {
                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);
            } finally{
                if (e_4) throw e_4.error;
            }
        }
    };
    InstrumentationBase.prototype.isEnabled = function() {
        return this._enabled;
    };
    return InstrumentationBase;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InstrumentationAbstract"]);
;
function isSupported(supportedVersions, version, includePrerelease) {
    if (typeof version === 'undefined') {
        // If we don't have the version, accept the wildcard case only
        return supportedVersions.includes('*');
    }
    return supportedVersions.some(function(supportedVersion) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$semver$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["satisfies"])(version, supportedVersion, {
            includePrerelease: includePrerelease
        });
    });
} //# sourceMappingURL=instrumentation.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "InstrumentationNodeModuleDefinition": (()=>InstrumentationNodeModuleDefinition)
});
var InstrumentationNodeModuleDefinition = function() {
    function InstrumentationNodeModuleDefinition(name, supportedVersions, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    patch, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    unpatch, files) {
        this.name = name;
        this.supportedVersions = supportedVersions;
        this.patch = patch;
        this.unpatch = unpatch;
        this.files = files || [];
    }
    return InstrumentationNodeModuleDefinition;
}();
;
 //# sourceMappingURL=instrumentationNodeModuleDefinition.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "InstrumentationNodeModuleFile": (()=>InstrumentationNodeModuleFile)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
var InstrumentationNodeModuleFile = function() {
    function InstrumentationNodeModuleFile(name, supportedVersions, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    patch, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    unpatch) {
        this.supportedVersions = supportedVersions;
        this.patch = patch;
        this.unpatch = unpatch;
        this.name = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["normalize"])(name);
    }
    return InstrumentationNodeModuleFile;
}();
;
 //# sourceMappingURL=instrumentationNodeModuleFile.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/types.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/types_internal.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=types_internal.js.map
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InstrumentationBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InstrumentationBase"]),
    "InstrumentationNodeModuleDefinition": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentationNodeModuleDefinition$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InstrumentationNodeModuleDefinition"]),
    "InstrumentationNodeModuleFile": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentationNodeModuleFile$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["InstrumentationNodeModuleFile"]),
    "isWrapped": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isWrapped"]),
    "registerInstrumentations": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$autoLoader$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerInstrumentations"]),
    "safeExecuteInTheMiddle": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["safeExecuteInTheMiddle"]),
    "safeExecuteInTheMiddleAsync": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["safeExecuteInTheMiddleAsync"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$autoLoader$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/autoLoader.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$platform$2f$node$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/instrumentation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentationNodeModuleDefinition$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleDefinition.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$instrumentationNodeModuleFile$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/instrumentationNodeModuleFile.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$types_internal$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/types_internal.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "InstrumentationBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InstrumentationBase"]),
    "InstrumentationNodeModuleDefinition": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InstrumentationNodeModuleDefinition"]),
    "InstrumentationNodeModuleFile": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InstrumentationNodeModuleFile"]),
    "isWrapped": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["isWrapped"]),
    "registerInstrumentations": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["registerInstrumentations"]),
    "safeExecuteInTheMiddle": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["safeExecuteInTheMiddle"]),
    "safeExecuteInTheMiddleAsync": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["safeExecuteInTheMiddleAsync"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$instrumentation$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/version.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // this is autogenerated file, see scripts/version-update.js
__turbopack_context__.s({
    "VERSION": (()=>VERSION)
});
var VERSION = '0.52.1'; //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/platform/node/OTLPTraceExporter.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPTraceExporter": (()=>OTLPTraceExporter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/baggage/utils.js [app-rsc] (ecmascript) <export * as baggageUtils>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/OTLPExporterNodeBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$transformer$2f$build$2f$esm$2f$protobuf$2f$serializers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-transformer/build/esm/protobuf/serializers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/version.js [app-rsc] (ecmascript)");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
;
var DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';
var DEFAULT_COLLECTOR_URL = "http://localhost:4318/" + DEFAULT_COLLECTOR_RESOURCE_PATH;
var USER_AGENT = {
    'User-Agent': "OTel-OTLP-Exporter-JavaScript/" + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["VERSION"]
};
/**
 * Collector Trace Exporter for Node with protobuf
 */ var OTLPTraceExporter = function(_super) {
    __extends(OTLPTraceExporter, _super);
    function OTLPTraceExporter(config) {
        if (config === void 0) {
            config = {};
        }
        var _this = _super.call(this, config, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$transformer$2f$build$2f$esm$2f$protobuf$2f$serializers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ProtobufTraceSerializer"], 'application/x-protobuf') || this;
        _this.headers = __assign(__assign(__assign(__assign({}, _this.headers), USER_AGENT), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__["baggageUtils"].parseKeyPairsIntoRecord((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_HEADERS)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseHeaders"])(config === null || config === void 0 ? void 0 : config.headers));
        return _this;
    }
    OTLPTraceExporter.prototype.getDefaultUrl = function(config) {
        return typeof config.url === 'string' ? config.url : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendRootPathToUrlIfNeeded"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendResourcePathToUrl"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_ENDPOINT, DEFAULT_COLLECTOR_RESOURCE_PATH) : DEFAULT_COLLECTOR_URL;
    };
    return OTLPTraceExporter;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterNodeBase"]);
;
 //# sourceMappingURL=OTLPTraceExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPTraceExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPTraceExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/platform/node/OTLPTraceExporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$proto$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "DEFAULT_EXPORT_BACKOFF_MULTIPLIER": (()=>DEFAULT_EXPORT_BACKOFF_MULTIPLIER),
    "DEFAULT_EXPORT_INITIAL_BACKOFF": (()=>DEFAULT_EXPORT_INITIAL_BACKOFF),
    "DEFAULT_EXPORT_MAX_ATTEMPTS": (()=>DEFAULT_EXPORT_MAX_ATTEMPTS),
    "DEFAULT_EXPORT_MAX_BACKOFF": (()=>DEFAULT_EXPORT_MAX_BACKOFF),
    "appendResourcePathToUrl": (()=>appendResourcePathToUrl),
    "appendRootPathToUrlIfNeeded": (()=>appendRootPathToUrlIfNeeded),
    "configureExporterTimeout": (()=>configureExporterTimeout),
    "invalidTimeout": (()=>invalidTimeout),
    "isExportRetryable": (()=>isExportRetryable),
    "parseHeaders": (()=>parseHeaders),
    "parseRetryAfterToMills": (()=>parseRetryAfterToMills)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
;
;
var DEFAULT_TRACE_TIMEOUT = 10000;
var DEFAULT_EXPORT_MAX_ATTEMPTS = 5;
var DEFAULT_EXPORT_INITIAL_BACKOFF = 1000;
var DEFAULT_EXPORT_MAX_BACKOFF = 5000;
var DEFAULT_EXPORT_BACKOFF_MULTIPLIER = 1.5;
function parseHeaders(partialHeaders) {
    if (partialHeaders === void 0) {
        partialHeaders = {};
    }
    var headers = {};
    Object.entries(partialHeaders).forEach(function(_a) {
        var _b = __read(_a, 2), key = _b[0], value = _b[1];
        if (typeof value !== 'undefined') {
            headers[key] = String(value);
        } else {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn("Header \"" + key + "\" has invalid value (" + value + ") and will be ignored");
        }
    });
    return headers;
}
function appendResourcePathToUrl(url, path) {
    if (!url.endsWith('/')) {
        url = url + '/';
    }
    return url + path;
}
function appendRootPathToUrlIfNeeded(url) {
    try {
        var parsedUrl = new URL(url);
        if (parsedUrl.pathname === '') {
            parsedUrl.pathname = parsedUrl.pathname + '/';
        }
        return parsedUrl.toString();
    } catch (_a) {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn("Could not parse export URL: '" + url + "'");
        return url;
    }
}
function configureExporterTimeout(timeoutMillis) {
    if (typeof timeoutMillis === 'number') {
        if (timeoutMillis <= 0) {
            // OTLP exporter configured timeout - using default value of 10000ms
            return invalidTimeout(timeoutMillis, DEFAULT_TRACE_TIMEOUT);
        }
        return timeoutMillis;
    } else {
        return getExporterTimeoutFromEnv();
    }
}
function getExporterTimeoutFromEnv() {
    var _a;
    var definedTimeout = Number((_a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_TIMEOUT) !== null && _a !== void 0 ? _a : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TIMEOUT);
    if (definedTimeout <= 0) {
        // OTLP exporter configured timeout - using default value of 10000ms
        return invalidTimeout(definedTimeout, DEFAULT_TRACE_TIMEOUT);
    } else {
        return definedTimeout;
    }
}
function invalidTimeout(timeout, defaultTimeout) {
    __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Timeout must be greater than 0', timeout);
    return defaultTimeout;
}
function isExportRetryable(statusCode) {
    var retryCodes = [
        429,
        502,
        503,
        504
    ];
    return retryCodes.includes(statusCode);
}
function parseRetryAfterToMills(retryAfter) {
    if (retryAfter == null) {
        return -1;
    }
    var seconds = Number.parseInt(retryAfter, 10);
    if (Number.isInteger(seconds)) {
        return seconds > 0 ? seconds * 1000 : -1;
    }
    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After#directives
    var delay = new Date(retryAfter).getTime() - Date.now();
    if (delay >= 0) {
        return delay;
    }
    return 0;
} //# sourceMappingURL=util.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/OTLPExporterBase.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPExporterBase": (()=>OTLPExporterBase)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/utils/callback.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
;
;
;
/**
 * Collector Exporter abstract base class
 */ var OTLPExporterBase = function() {
    /**
     * @param config
     */ function OTLPExporterBase(config) {
        if (config === void 0) {
            config = {};
        }
        this._sendingPromises = [];
        this.url = this.getDefaultUrl(config);
        if (typeof config.hostname === 'string') {
            this.hostname = config.hostname;
        }
        this.shutdown = this.shutdown.bind(this);
        this._shutdownOnce = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$utils$2f$callback$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BindOnceFuture"](this._shutdown, this);
        this._concurrencyLimit = typeof config.concurrencyLimit === 'number' ? config.concurrencyLimit : 30;
        this.timeoutMillis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configureExporterTimeout"])(config.timeoutMillis);
        // platform dependent
        this.onInit(config);
    }
    /**
     * Export items.
     * @param items
     * @param resultCallback
     */ OTLPExporterBase.prototype.export = function(items, resultCallback) {
        if (this._shutdownOnce.isCalled) {
            resultCallback({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                error: new Error('Exporter has been shutdown')
            });
            return;
        }
        if (this._sendingPromises.length >= this._concurrencyLimit) {
            resultCallback({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                error: new Error('Concurrent export limit reached')
            });
            return;
        }
        this._export(items).then(function() {
            resultCallback({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS
            });
        }).catch(function(error) {
            resultCallback({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                error: error
            });
        });
    };
    OTLPExporterBase.prototype._export = function(items) {
        var _this = this;
        return new Promise(function(resolve, reject) {
            try {
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('items to be sent', items);
                _this.send(items, resolve, reject);
            } catch (e) {
                reject(e);
            }
        });
    };
    /**
     * Shutdown the exporter.
     */ OTLPExporterBase.prototype.shutdown = function() {
        return this._shutdownOnce.call();
    };
    /**
     * Exports any pending spans in the exporter
     */ OTLPExporterBase.prototype.forceFlush = function() {
        return Promise.all(this._sendingPromises).then(function() {
        /** ignore resolved values */ });
    };
    /**
     * Called by _shutdownOnce with BindOnceFuture
     */ OTLPExporterBase.prototype._shutdown = function() {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('shutdown started');
        this.onShutdown();
        return this.forceFlush();
    };
    return OTLPExporterBase;
}();
;
 //# sourceMappingURL=OTLPExporterBase.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/types.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompressionAlgorithm": (()=>CompressionAlgorithm)
});
var CompressionAlgorithm;
(function(CompressionAlgorithm) {
    CompressionAlgorithm["NONE"] = "none";
    CompressionAlgorithm["GZIP"] = "gzip";
})(CompressionAlgorithm || (CompressionAlgorithm = {})); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/types.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPExporterError": (()=>OTLPExporterError)
});
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
/**
 * Interface for handling error
 */ var OTLPExporterError = function(_super) {
    __extends(OTLPExporterError, _super);
    function OTLPExporterError(message, code, data) {
        var _this = _super.call(this, message) || this;
        _this.name = 'OTLPExporterError';
        _this.data = data;
        _this.code = code;
        return _this;
    }
    return OTLPExporterError;
}(Error);
;
 //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/util.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "configureCompression": (()=>configureCompression),
    "createHttpAgent": (()=>createHttpAgent),
    "sendWithHttp": (()=>sendWithHttp)
});
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/url [external] (url, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/http [external] (http, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/https [external] (https, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/zlib [external] (zlib, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/stream [external] (stream, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
;
;
;
;
;
;
;
function sendWithHttp(collector, data, contentType, onSuccess, onError) {
    var exporterTimeout = collector.timeoutMillis;
    var parsedUrl = new __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["URL"](collector.url);
    var nodeVersion = Number(process.versions.node.split('.')[0]);
    var retryTimer;
    var req;
    var reqIsDestroyed = false;
    var exporterTimer = setTimeout(function() {
        clearTimeout(retryTimer);
        reqIsDestroyed = true;
        if (req.destroyed) {
            var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
            onError(err);
        } else {
            // req.abort() was deprecated since v14
            nodeVersion >= 14 ? req.destroy() : req.abort();
        }
    }, exporterTimeout);
    var options = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port,
        path: parsedUrl.pathname,
        method: 'POST',
        headers: __assign({
            'Content-Type': contentType
        }, collector.headers),
        agent: collector.agent
    };
    var request = parsedUrl.protocol === 'http:' ? __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["request"] : __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__["request"];
    var sendWithRetry = function(retries, minDelay) {
        if (retries === void 0) {
            retries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_MAX_ATTEMPTS"];
        }
        if (minDelay === void 0) {
            minDelay = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_INITIAL_BACKOFF"];
        }
        req = request(options, function(res) {
            var responseData = '';
            res.on('data', function(chunk) {
                return responseData += chunk;
            });
            res.on('aborted', function() {
                if (reqIsDestroyed) {
                    var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
                    onError(err);
                }
            });
            res.on('end', function() {
                if (reqIsDestroyed === false) {
                    if (res.statusCode && res.statusCode < 299) {
                        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug("statusCode: " + res.statusCode, responseData);
                        onSuccess();
                        // clear all timers since request was completed and promise was resolved
                        clearTimeout(exporterTimer);
                        clearTimeout(retryTimer);
                    } else if (res.statusCode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isExportRetryable"])(res.statusCode) && retries > 0) {
                        var retryTime = void 0;
                        minDelay = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_BACKOFF_MULTIPLIER"] * minDelay;
                        // retry after interval specified in Retry-After header
                        if (res.headers['retry-after']) {
                            retryTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseRetryAfterToMills"])(res.headers['retry-after']);
                        } else {
                            // exponential backoff with jitter
                            retryTime = Math.round(Math.random() * (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_MAX_BACKOFF"] - minDelay) + minDelay);
                        }
                        retryTimer = setTimeout(function() {
                            sendWithRetry(retries - 1, minDelay);
                        }, retryTime);
                    } else {
                        var error = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"](res.statusMessage, res.statusCode, responseData);
                        onError(error);
                        // clear all timers since request was completed and promise was resolved
                        clearTimeout(exporterTimer);
                        clearTimeout(retryTimer);
                    }
                }
            });
        });
        req.on('error', function(error) {
            if (reqIsDestroyed) {
                var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout', error.code);
                onError(err);
            } else {
                onError(error);
            }
            clearTimeout(exporterTimer);
            clearTimeout(retryTimer);
        });
        req.on('abort', function() {
            if (reqIsDestroyed) {
                var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
                onError(err);
            }
            clearTimeout(exporterTimer);
            clearTimeout(retryTimer);
        });
        switch(collector.compression){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"].GZIP:
                {
                    req.setHeader('Content-Encoding', 'gzip');
                    var dataStream = readableFromUnit8Array(data);
                    dataStream.on('error', onError).pipe((0, __TURBOPACK__imported__module__$5b$externals$5d2f$zlib__$5b$external$5d$__$28$zlib$2c$__cjs$29$__["createGzip"])()).on('error', onError).pipe(req);
                    break;
                }
            default:
                req.end(Buffer.from(data));
                break;
        }
    };
    sendWithRetry();
}
function readableFromUnit8Array(buff) {
    var readable = new __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["Readable"]();
    readable.push(buff);
    readable.push(null);
    return readable;
}
function createHttpAgent(config) {
    if (config.httpAgentOptions && config.keepAlive === false) {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('httpAgentOptions is used only when keepAlive is true');
        return undefined;
    }
    if (config.keepAlive === false || !config.url) return undefined;
    try {
        var parsedUrl = new __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["URL"](config.url);
        var Agent = parsedUrl.protocol === 'http:' ? __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__["Agent"] : __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__["Agent"];
        return new Agent(__assign({
            keepAlive: true
        }, config.httpAgentOptions));
    } catch (err) {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].error("collector exporter failed to create http agent. err: " + err.message);
        return undefined;
    }
}
function configureCompression(compression) {
    if (compression) {
        return compression;
    } else {
        var definedCompression = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_COMPRESSION || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_COMPRESSION;
        return definedCompression === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"].GZIP ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"].GZIP : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"].NONE;
    }
} //# sourceMappingURL=util.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/OTLPExporterNodeBase.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPExporterNodeBase": (()=>OTLPExporterNodeBase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/OTLPExporterBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/baggage/utils.js [app-rsc] (ecmascript) <export * as baggageUtils>");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
;
;
;
;
;
/**
 * Collector Metric Exporter abstract base class
 */ var OTLPExporterNodeBase = function(_super) {
    __extends(OTLPExporterNodeBase, _super);
    function OTLPExporterNodeBase(config, serializer, contentType) {
        if (config === void 0) {
            config = {};
        }
        var _this = _super.call(this, config) || this;
        _this.DEFAULT_HEADERS = {};
        _this._contentType = contentType;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        if (config.metadata) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].warn('Metadata cannot be set when using http');
        }
        _this.headers = Object.assign(_this.DEFAULT_HEADERS, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseHeaders"])(config.headers), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__["baggageUtils"].parseKeyPairsIntoRecord((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_HEADERS));
        _this.agent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createHttpAgent"])(config);
        _this.compression = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configureCompression"])(config.compression);
        _this._serializer = serializer;
        return _this;
    }
    OTLPExporterNodeBase.prototype.onInit = function(_config) {};
    OTLPExporterNodeBase.prototype.send = function(objects, onSuccess, onError) {
        var _this = this;
        if (this._shutdownOnce.isCalled) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('Shutdown already started. Cannot send objects');
            return;
        }
        var promise = new Promise(function(resolve, reject) {
            var _a;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithHttp"])(_this, (_a = _this._serializer.serializeRequest(objects)) !== null && _a !== void 0 ? _a : new Uint8Array(), _this._contentType, resolve, reject);
        }).then(onSuccess, onError);
        this._sendingPromises.push(promise);
        var popPromise = function() {
            var index = _this._sendingPromises.indexOf(promise);
            _this._sendingPromises.splice(index, 1);
        };
        promise.then(popPromise, popPromise);
    };
    OTLPExporterNodeBase.prototype.onShutdown = function() {};
    return OTLPExporterNodeBase;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterBase"]);
;
 //# sourceMappingURL=OTLPExporterNodeBase.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/browser/util.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "sendWithBeacon": (()=>sendWithBeacon),
    "sendWithXhr": (()=>sendWithXhr)
});
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __read = this && this.__read || function(o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while((n === void 0 || n-- > 0) && !(r = i.next()).done)ar.push(r.value);
    } catch (error) {
        e = {
            error: error
        };
    } finally{
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        } finally{
            if (e) throw e.error;
        }
    }
    return ar;
};
;
;
;
function sendWithBeacon(body, url, blobPropertyBag, onSuccess, onError) {
    if (navigator.sendBeacon(url, new Blob([
        body
    ], blobPropertyBag))) {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('sendBeacon - can send', body);
        onSuccess();
    } else {
        var error = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]("sendBeacon - cannot send " + body);
        onError(error);
    }
}
function sendWithXhr(body, url, headers, exporterTimeout, onSuccess, onError) {
    var retryTimer;
    var xhr;
    var reqIsDestroyed = false;
    var exporterTimer = setTimeout(function() {
        clearTimeout(retryTimer);
        reqIsDestroyed = true;
        if (xhr.readyState === XMLHttpRequest.DONE) {
            var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
            onError(err);
        } else {
            xhr.abort();
        }
    }, exporterTimeout);
    var sendWithRetry = function(retries, minDelay) {
        if (retries === void 0) {
            retries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_MAX_ATTEMPTS"];
        }
        if (minDelay === void 0) {
            minDelay = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_INITIAL_BACKOFF"];
        }
        xhr = new XMLHttpRequest();
        xhr.open('POST', url);
        var defaultHeaders = {
            Accept: 'application/json',
            'Content-Type': 'application/json'
        };
        Object.entries(__assign(__assign({}, defaultHeaders), headers)).forEach(function(_a) {
            var _b = __read(_a, 2), k = _b[0], v = _b[1];
            xhr.setRequestHeader(k, v);
        });
        xhr.send(body);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE && reqIsDestroyed === false) {
                if (xhr.status >= 200 && xhr.status <= 299) {
                    __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('xhr success', body);
                    onSuccess();
                    clearTimeout(exporterTimer);
                    clearTimeout(retryTimer);
                } else if (xhr.status && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isExportRetryable"])(xhr.status) && retries > 0) {
                    var retryTime = void 0;
                    minDelay = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_BACKOFF_MULTIPLIER"] * minDelay;
                    // retry after interval specified in Retry-After header
                    if (xhr.getResponseHeader('Retry-After')) {
                        retryTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseRetryAfterToMills"])(xhr.getResponseHeader('Retry-After'));
                    } else {
                        // exponential backoff with jitter
                        retryTime = Math.round(Math.random() * (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["DEFAULT_EXPORT_MAX_BACKOFF"] - minDelay) + minDelay);
                    }
                    retryTimer = setTimeout(function() {
                        sendWithRetry(retries - 1, minDelay);
                    }, retryTime);
                } else {
                    var error = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]("Failed to export with XHR (status: " + xhr.status + ")", xhr.status);
                    onError(error);
                    clearTimeout(exporterTimer);
                    clearTimeout(retryTimer);
                }
            }
        };
        xhr.onabort = function() {
            if (reqIsDestroyed) {
                var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
                onError(err);
            }
            clearTimeout(exporterTimer);
            clearTimeout(retryTimer);
        };
        xhr.onerror = function() {
            if (reqIsDestroyed) {
                var err = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]('Request Timeout');
                onError(err);
            }
            clearTimeout(exporterTimer);
            clearTimeout(retryTimer);
        };
    };
    sendWithRetry();
} //# sourceMappingURL=util.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/browser/OTLPExporterBrowserBase.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPExporterBrowserBase": (()=>OTLPExporterBrowserBase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/OTLPExporterBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/browser/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/baggage/utils.js [app-rsc] (ecmascript) <export * as baggageUtils>");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
;
;
/**
 * Collector Metric Exporter abstract base class
 */ var OTLPExporterBrowserBase = function(_super) {
    __extends(OTLPExporterBrowserBase, _super);
    /**
     * @param config
     * @param serializer
     * @param contentType
     */ function OTLPExporterBrowserBase(config, serializer, contentType) {
        if (config === void 0) {
            config = {};
        }
        var _this = _super.call(this, config) || this;
        _this._useXHR = false;
        _this._serializer = serializer;
        _this._contentType = contentType;
        _this._useXHR = !!config.headers || typeof navigator.sendBeacon !== 'function';
        if (_this._useXHR) {
            _this._headers = Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseHeaders"])(config.headers), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__["baggageUtils"].parseKeyPairsIntoRecord((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_HEADERS));
        } else {
            _this._headers = {};
        }
        return _this;
    }
    OTLPExporterBrowserBase.prototype.onInit = function() {};
    OTLPExporterBrowserBase.prototype.onShutdown = function() {};
    OTLPExporterBrowserBase.prototype.send = function(items, onSuccess, onError) {
        var _this = this;
        var _a;
        if (this._shutdownOnce.isCalled) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('Shutdown already started. Cannot send objects');
            return;
        }
        var body = (_a = this._serializer.serializeRequest(items)) !== null && _a !== void 0 ? _a : new Uint8Array();
        var promise = new Promise(function(resolve, reject) {
            if (_this._useXHR) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithXhr"])(body, _this.url, __assign(__assign({}, _this._headers), {
                    'Content-Type': _this._contentType
                }), _this.timeoutMillis, resolve, reject);
            } else {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithBeacon"])(body, _this.url, {
                    type: _this._contentType
                }, resolve, reject);
            }
        }).then(onSuccess, onError);
        this._sendingPromises.push(promise);
        var popPromise = function() {
            var index = _this._sendingPromises.indexOf(promise);
            _this._sendingPromises.splice(index, 1);
        };
        promise.then(popPromise, popPromise);
    };
    return OTLPExporterBrowserBase;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterBase"]);
;
 //# sourceMappingURL=OTLPExporterBrowserBase.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompressionAlgorithm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"]),
    "OTLPExporterBrowserBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$OTLPExporterBrowserBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterBrowserBase"]),
    "OTLPExporterNodeBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterNodeBase"]),
    "configureCompression": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configureCompression"]),
    "createHttpAgent": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createHttpAgent"]),
    "sendWithHttp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithHttp"]),
    "sendWithXhr": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithXhr"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/OTLPExporterNodeBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$OTLPExporterBrowserBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/browser/OTLPExporterBrowserBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$browser$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/browser/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompressionAlgorithm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["CompressionAlgorithm"]),
    "OTLPExporterBrowserBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterBrowserBase"]),
    "OTLPExporterNodeBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterNodeBase"]),
    "configureCompression": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["configureCompression"]),
    "createHttpAgent": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createHttpAgent"]),
    "sendWithHttp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["sendWithHttp"]),
    "sendWithXhr": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["sendWithXhr"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompressionAlgorithm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CompressionAlgorithm"]),
    "OTLPExporterBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterBase"]),
    "OTLPExporterBrowserBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterBrowserBase"]),
    "OTLPExporterError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterError"]),
    "OTLPExporterNodeBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterNodeBase"]),
    "appendResourcePathToUrl": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendResourcePathToUrl"]),
    "appendRootPathToUrlIfNeeded": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendRootPathToUrlIfNeeded"]),
    "configureCompression": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configureCompression"]),
    "configureExporterTimeout": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["configureExporterTimeout"]),
    "createHttpAgent": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createHttpAgent"]),
    "invalidTimeout": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["invalidTimeout"]),
    "parseHeaders": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseHeaders"]),
    "sendWithHttp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithHttp"]),
    "sendWithXhr": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["sendWithXhr"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$OTLPExporterBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/OTLPExporterBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompressionAlgorithm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["CompressionAlgorithm"]),
    "OTLPExporterBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterBase"]),
    "OTLPExporterBrowserBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterBrowserBase"]),
    "OTLPExporterError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterError"]),
    "OTLPExporterNodeBase": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPExporterNodeBase"]),
    "appendResourcePathToUrl": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["appendResourcePathToUrl"]),
    "appendRootPathToUrlIfNeeded": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["appendRootPathToUrlIfNeeded"]),
    "configureCompression": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["configureCompression"]),
    "configureExporterTimeout": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["configureExporterTimeout"]),
    "createHttpAgent": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createHttpAgent"]),
    "invalidTimeout": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["invalidTimeout"]),
    "parseHeaders": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["parseHeaders"]),
    "sendWithHttp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["sendWithHttp"]),
    "sendWithXhr": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["sendWithXhr"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/version.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ // this is autogenerated file, see scripts/version-update.js
__turbopack_context__.s({
    "VERSION": (()=>VERSION)
});
var VERSION = '0.52.1'; //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/OTLPTraceExporter.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "OTLPTraceExporter": (()=>OTLPTraceExporter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/baggage/utils.js [app-rsc] (ecmascript) <export * as baggageUtils>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/platform/node/OTLPExporterNodeBase.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/version.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$transformer$2f$build$2f$esm$2f$json$2f$serializers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/otlp-transformer/build/esm/json/serializers.js [app-rsc] (ecmascript)");
var __extends = this && this.__extends || function() {
    var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || ({
            __proto__: []
        }) instanceof Array && function(d, b) {
            d.__proto__ = b;
        } || function(d, b) {
            for(var p in b)if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];
        };
        return extendStatics(d, b);
    };
    return function(d, b) {
        if (typeof b !== "function" && b !== null) throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
            this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
}();
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
;
;
var DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/traces';
var DEFAULT_COLLECTOR_URL = "http://localhost:4318/" + DEFAULT_COLLECTOR_RESOURCE_PATH;
var USER_AGENT = {
    'User-Agent': "OTel-OTLP-Exporter-JavaScript/" + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["VERSION"]
};
/**
 * Collector Trace Exporter for Node
 */ var OTLPTraceExporter = function(_super) {
    __extends(OTLPTraceExporter, _super);
    function OTLPTraceExporter(config) {
        if (config === void 0) {
            config = {};
        }
        var _this = _super.call(this, config, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$transformer$2f$build$2f$esm$2f$json$2f$serializers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["JsonTraceSerializer"], 'application/json') || this;
        _this.headers = __assign(__assign(__assign(__assign({}, _this.headers), USER_AGENT), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$baggage$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__baggageUtils$3e$__["baggageUtils"].parseKeyPairsIntoRecord((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_HEADERS)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["parseHeaders"])(config === null || config === void 0 ? void 0 : config.headers));
        return _this;
    }
    OTLPTraceExporter.prototype.getDefaultUrl = function(config) {
        return typeof config.url === 'string' ? config.url : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendRootPathToUrlIfNeeded"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["appendResourcePathToUrl"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_OTLP_ENDPOINT, DEFAULT_COLLECTOR_RESOURCE_PATH) : DEFAULT_COLLECTOR_URL;
    };
    return OTLPTraceExporter;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$otlp$2d$exporter$2d$base$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPExporterNodeBase$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPExporterNodeBase"]);
;
 //# sourceMappingURL=OTLPTraceExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPTraceExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$OTLPTraceExporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/OTLPTraceExporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/node/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/platform/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "OTLPTraceExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["OTLPTraceExporter"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$trace$2d$otlp$2d$http$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/create-service-client-constructor.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createServiceClientConstructor = void 0;
const grpc = __turbopack_context__.r("[project]/node_modules/@grpc/grpc-js/build/src/index.js [app-rsc] (ecmascript)");
/**
 * Creates a unary service client constructor that, when instantiated, does not serialize/deserialize anything.
 * Allows for passing in {@link Buffer} directly, serialization can be handled via protobufjs or custom implementations.
 *
 * @param path service path
 * @param name service name
 */ function createServiceClientConstructor(path, name) {
    const serviceDefinition = {
        export: {
            path: path,
            requestStream: false,
            responseStream: false,
            requestSerialize: (arg)=>{
                return arg;
            },
            requestDeserialize: (arg)=>{
                return arg;
            },
            responseSerialize: (arg)=>{
                return arg;
            },
            responseDeserialize: (arg)=>{
                return arg;
            }
        }
    };
    return grpc.makeGenericClientConstructor(serviceDefinition, name);
}
exports.createServiceClientConstructor = createServiceClientConstructor; //# sourceMappingURL=create-service-client-constructor.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/grpc-exporter-transport.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.GrpcExporterTransport = exports.createEmptyMetadata = exports.createSslCredentials = exports.createInsecureCredentials = void 0;
// values taken from '@grpc/grpc-js` so that we don't need to require/import it.
const GRPC_COMPRESSION_NONE = 0;
const GRPC_COMPRESSION_GZIP = 2;
function toGrpcCompression(compression) {
    return compression === 'gzip' ? GRPC_COMPRESSION_GZIP : GRPC_COMPRESSION_NONE;
}
function createInsecureCredentials() {
    // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.
    const { credentials } = __turbopack_context__.r("[project]/node_modules/@grpc/grpc-js/build/src/index.js [app-rsc] (ecmascript)");
    return credentials.createInsecure();
}
exports.createInsecureCredentials = createInsecureCredentials;
function createSslCredentials(rootCert, privateKey, certChain) {
    // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.
    const { credentials } = __turbopack_context__.r("[project]/node_modules/@grpc/grpc-js/build/src/index.js [app-rsc] (ecmascript)");
    return credentials.createSsl(rootCert, privateKey, certChain);
}
exports.createSslCredentials = createSslCredentials;
function createEmptyMetadata() {
    // Lazy-load so that we don't need to require/import '@grpc/grpc-js' before it can be wrapped by instrumentation.
    const { Metadata } = __turbopack_context__.r("[project]/node_modules/@grpc/grpc-js/build/src/index.js [app-rsc] (ecmascript)");
    return new Metadata();
}
exports.createEmptyMetadata = createEmptyMetadata;
class GrpcExporterTransport {
    constructor(_parameters){
        this._parameters = _parameters;
    }
    shutdown() {
        var _a;
        (_a = this._client) === null || _a === void 0 ? void 0 : _a.close();
    }
    send(data) {
        // We need to make a for gRPC
        const buffer = Buffer.from(data);
        if (this._client == null) {
            // Lazy require to ensure that grpc is not loaded before instrumentations can wrap it
            const { createServiceClientConstructor } = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/create-service-client-constructor.js [app-rsc] (ecmascript)");
            try {
                this._metadata = this._parameters.metadata();
            } catch (error) {
                return Promise.resolve({
                    status: 'failure',
                    error: error
                });
            }
            const clientConstructor = createServiceClientConstructor(this._parameters.grpcPath, this._parameters.grpcName);
            try {
                this._client = new clientConstructor(this._parameters.address, this._parameters.credentials(), {
                    'grpc.default_compression_algorithm': toGrpcCompression(this._parameters.compression)
                });
            } catch (error) {
                return Promise.resolve({
                    status: 'failure',
                    error: error
                });
            }
        }
        return new Promise((resolve)=>{
            // this will always be defined
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            const deadline = Date.now() + this._parameters.timeoutMillis;
            // this should never happen
            if (this._metadata == null) {
                return resolve({
                    error: new Error('metadata was null'),
                    status: 'failure'
                });
            }
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore The gRPC client constructor is created on runtime, so we don't have any types for the resulting client.
            this._client.export(buffer, this._metadata, {
                deadline: deadline
            }, (err, response)=>{
                if (err) {
                    resolve({
                        status: 'failure',
                        error: err
                    });
                } else {
                    resolve({
                        data: response,
                        status: 'success'
                    });
                }
            });
        });
    }
}
exports.GrpcExporterTransport = GrpcExporterTransport; //# sourceMappingURL=grpc-exporter-transport.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/util.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.configureCompression = exports.getCredentialsFromEnvironment = exports.configureCredentials = exports.validateAndNormalizeUrl = exports.DEFAULT_COLLECTOR_URL = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
const url_1 = __turbopack_context__.r("[externals]/url [external] (url, cjs)");
const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
const otlp_exporter_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript)");
const grpc_exporter_transport_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/grpc-exporter-transport.js [app-rsc] (ecmascript)");
exports.DEFAULT_COLLECTOR_URL = 'http://localhost:4317';
function validateAndNormalizeUrl(url) {
    var _a;
    const hasProtocol = url.match(/^([\w]{1,8}):\/\//);
    if (!hasProtocol) {
        url = `https://${url}`;
    }
    const target = new url_1.URL(url);
    if (target.protocol === 'unix:') {
        return url;
    }
    if (target.pathname && target.pathname !== '/') {
        api_1.diag.warn('URL path should not be set when using grpc, the path part of the URL will be ignored.');
    }
    if (target.protocol !== '' && !((_a = target.protocol) === null || _a === void 0 ? void 0 : _a.match(/^(http)s?:$/))) {
        api_1.diag.warn('URL protocol should be http(s)://. Using http://.');
    }
    return target.host;
}
exports.validateAndNormalizeUrl = validateAndNormalizeUrl;
function configureCredentials(credentials, endpoint) {
    let insecure;
    if (credentials) {
        return credentials;
    } else if (endpoint.startsWith('https://')) {
        insecure = false;
    } else if (endpoint.startsWith('http://') || endpoint === exports.DEFAULT_COLLECTOR_URL) {
        insecure = true;
    } else {
        insecure = getSecurityFromEnv();
    }
    if (insecure) {
        return (0, grpc_exporter_transport_1.createInsecureCredentials)();
    } else {
        return getCredentialsFromEnvironment();
    }
}
exports.configureCredentials = configureCredentials;
function getSecurityFromEnv() {
    const definedInsecure = (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_INSECURE || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_INSECURE;
    if (definedInsecure) {
        return definedInsecure.toLowerCase() === 'true';
    } else {
        return false;
    }
}
/**
 * Exported for testing
 */ function getCredentialsFromEnvironment() {
    const rootCert = retrieveRootCert();
    const privateKey = retrievePrivateKey();
    const certChain = retrieveCertChain();
    return (0, grpc_exporter_transport_1.createSslCredentials)(rootCert, privateKey, certChain);
}
exports.getCredentialsFromEnvironment = getCredentialsFromEnvironment;
function retrieveRootCert() {
    const rootCertificate = (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_CERTIFICATE;
    if (rootCertificate) {
        try {
            return fs.readFileSync(path.resolve(process.cwd(), rootCertificate));
        } catch (_a) {
            api_1.diag.warn('Failed to read root certificate file');
            return undefined;
        }
    } else {
        return undefined;
    }
}
function retrievePrivateKey() {
    const clientKey = (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_CLIENT_KEY;
    if (clientKey) {
        try {
            return fs.readFileSync(path.resolve(process.cwd(), clientKey));
        } catch (_a) {
            api_1.diag.warn('Failed to read client certificate private key file');
            return undefined;
        }
    } else {
        return undefined;
    }
}
function retrieveCertChain() {
    const clientChain = (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE;
    if (clientChain) {
        try {
            return fs.readFileSync(path.resolve(process.cwd(), clientChain));
        } catch (_a) {
            api_1.diag.warn('Failed to read client certificate chain file');
            return undefined;
        }
    } else {
        return undefined;
    }
}
function configureCompression(compression) {
    if (compression != null) {
        return compression;
    }
    const envCompression = (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_COMPRESSION || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_COMPRESSION;
    if (envCompression === 'gzip') {
        return otlp_exporter_base_1.CompressionAlgorithm.GZIP;
    } else if (envCompression === 'none') {
        return otlp_exporter_base_1.CompressionAlgorithm.NONE;
    }
    api_1.diag.warn('Unknown compression "' + envCompression + '", falling back to "none"');
    return otlp_exporter_base_1.CompressionAlgorithm.NONE;
}
exports.configureCompression = configureCompression; //# sourceMappingURL=util.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/OTLPGRPCExporterNodeBase.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.OTLPGRPCExporterNodeBase = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const otlp_exporter_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-exporter-base/build/esm/index.js [app-rsc] (ecmascript)");
const grpc_exporter_transport_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/grpc-exporter-transport.js [app-rsc] (ecmascript)");
const util_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/util.js [app-rsc] (ecmascript)");
/**
 * OTLP Exporter abstract base class
 */ class OTLPGRPCExporterNodeBase extends otlp_exporter_base_1.OTLPExporterBase {
    constructor(config = {}, signalSpecificMetadata, grpcName, grpcPath, serializer){
        var _a;
        super(config);
        this.grpcQueue = [];
        this._serializer = serializer;
        if (config.headers) {
            api_1.diag.warn('Headers cannot be set when using grpc');
        }
        const nonSignalSpecificMetadata = core_1.baggageUtils.parseKeyPairsIntoRecord((0, core_1.getEnv)().OTEL_EXPORTER_OTLP_HEADERS);
        const rawMetadata = Object.assign({}, nonSignalSpecificMetadata, signalSpecificMetadata);
        let credentialProvider = ()=>{
            return (0, util_1.configureCredentials)(undefined, this.getUrlFromConfig(config));
        };
        if (config.credentials != null) {
            const credentials = config.credentials;
            credentialProvider = ()=>{
                return credentials;
            };
        }
        // Ensure we don't modify the original.
        const configMetadata = (_a = config.metadata) === null || _a === void 0 ? void 0 : _a.clone();
        const metadataProvider = ()=>{
            const metadata = configMetadata !== null && configMetadata !== void 0 ? configMetadata : (0, grpc_exporter_transport_1.createEmptyMetadata)();
            for (const [key, value] of Object.entries(rawMetadata)){
                // only override with env var data if the key has no values.
                // not using Metadata.merge() as it will keep both values.
                if (metadata.get(key).length < 1) {
                    metadata.set(key, value);
                }
            }
            return metadata;
        };
        this.compression = (0, util_1.configureCompression)(config.compression);
        this._transport = new grpc_exporter_transport_1.GrpcExporterTransport({
            address: this.getDefaultUrl(config),
            compression: this.compression,
            credentials: credentialProvider,
            grpcName: grpcName,
            grpcPath: grpcPath,
            metadata: metadataProvider,
            timeoutMillis: this.timeoutMillis
        });
    }
    onInit() {
    // Intentionally left empty; nothing to do.
    }
    onShutdown() {
        this._transport.shutdown();
    }
    send(objects, onSuccess, onError) {
        if (this._shutdownOnce.isCalled) {
            api_1.diag.debug('Shutdown already started. Cannot send objects');
            return;
        }
        const data = this._serializer.serializeRequest(objects);
        if (data == null) {
            onError(new Error('Could not serialize message'));
            return;
        }
        const promise = this._transport.send(data).then((response)=>{
            if (response.status === 'success') {
                onSuccess();
                return;
            }
            if (response.status === 'failure' && response.error) {
                onError(response.error);
            }
            onError(new otlp_exporter_base_1.OTLPExporterError('Export failed with unknown error'));
        }, onError);
        this._sendingPromises.push(promise);
        const popPromise = ()=>{
            const index = this._sendingPromises.indexOf(promise);
            this._sendingPromises.splice(index, 1);
        };
        promise.then(popPromise, popPromise);
    }
}
exports.OTLPGRPCExporterNodeBase = OTLPGRPCExporterNodeBase; //# sourceMappingURL=OTLPGRPCExporterNodeBase.js.map
}}),
"[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.validateAndNormalizeUrl = exports.DEFAULT_COLLECTOR_URL = exports.OTLPGRPCExporterNodeBase = void 0;
var OTLPGRPCExporterNodeBase_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/OTLPGRPCExporterNodeBase.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "OTLPGRPCExporterNodeBase", {
    enumerable: true,
    get: function() {
        return OTLPGRPCExporterNodeBase_1.OTLPGRPCExporterNodeBase;
    }
});
var util_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/util.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "DEFAULT_COLLECTOR_URL", {
    enumerable: true,
    get: function() {
        return util_1.DEFAULT_COLLECTOR_URL;
    }
});
Object.defineProperty(exports, "validateAndNormalizeUrl", {
    enumerable: true,
    get: function() {
        return util_1.validateAndNormalizeUrl;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/version.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.VERSION = void 0;
// this is autogenerated file, see scripts/version-update.js
exports.VERSION = '0.52.1'; //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/OTLPTraceExporter.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.OTLPTraceExporter = void 0;
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const otlp_grpc_exporter_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-grpc-exporter-base/build/src/index.js [app-rsc] (ecmascript)");
const otlp_transformer_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/otlp-transformer/build/esm/index.js [app-rsc] (ecmascript)");
const version_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/version.js [app-rsc] (ecmascript)");
const USER_AGENT = {
    'User-Agent': `OTel-OTLP-Exporter-JavaScript/${version_1.VERSION}`
};
/**
 * OTLP Trace Exporter for Node
 */ class OTLPTraceExporter extends otlp_grpc_exporter_base_1.OTLPGRPCExporterNodeBase {
    constructor(config = {}){
        const signalSpecificMetadata = Object.assign(Object.assign({}, USER_AGENT), core_1.baggageUtils.parseKeyPairsIntoRecord((0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_HEADERS));
        super(config, signalSpecificMetadata, 'TraceExportService', '/opentelemetry.proto.collector.trace.v1.TraceService/Export', otlp_transformer_1.ProtobufTraceSerializer);
    }
    getDefaultUrl(config) {
        return (0, otlp_grpc_exporter_base_1.validateAndNormalizeUrl)(this.getUrlFromConfig(config));
    }
    getUrlFromConfig(config) {
        if (typeof config.url === 'string') {
            return config.url;
        }
        return (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_ENDPOINT || (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_ENDPOINT || otlp_grpc_exporter_base_1.DEFAULT_COLLECTOR_URL;
    }
}
exports.OTLPTraceExporter = OTLPTraceExporter; //# sourceMappingURL=OTLPTraceExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
__exportStar(__turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/OTLPTraceExporter.js [app-rsc] (ecmascript)"), exports); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({});
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/util.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "prepareSend": (()=>prepareSend)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/http [external] (http, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/https [external] (https, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/url [external] (url, cjs)");
var __assign = this && this.__assign || function() {
    __assign = Object.assign || function(t) {
        for(var s, i = 1, n = arguments.length; i < n; i++){
            s = arguments[i];
            for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
;
;
;
;
;
function prepareSend(urlStr, headers) {
    var urlOpts = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$url__$5b$external$5d$__$28$url$2c$__cjs$29$__["parse"])(urlStr);
    var reqOpts = Object.assign({
        method: 'POST',
        headers: __assign({
            'Content-Type': 'application/json'
        }, headers)
    }, urlOpts);
    /**
     * Send spans to the remote Zipkin service.
     */ return function send(zipkinSpans, done) {
        if (zipkinSpans.length === 0) {
            __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('Zipkin send with empty spans');
            return done({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS
            });
        }
        var request = (reqOpts.protocol === 'http:' ? __TURBOPACK__imported__module__$5b$externals$5d2f$http__$5b$external$5d$__$28$http$2c$__cjs$29$__ : __TURBOPACK__imported__module__$5b$externals$5d2f$https__$5b$external$5d$__$28$https$2c$__cjs$29$__).request;
        var req = request(reqOpts, function(res) {
            var rawData = '';
            res.on('data', function(chunk) {
                rawData += chunk;
            });
            res.on('end', function() {
                var statusCode = res.statusCode || 0;
                __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug("Zipkin response status code: " + statusCode + ", body: " + rawData);
                // Consider 2xx and 3xx as success.
                if (statusCode < 400) {
                    return done({
                        code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].SUCCESS
                    });
                // Consider 4xx as failed non-retryable.
                } else {
                    return done({
                        code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                        error: new Error("Got unexpected status code from zipkin: " + statusCode)
                    });
                }
            });
        });
        req.on('error', function(error) {
            return done({
                code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                error: error
            });
        });
        // Issue request to remote service
        var payload = JSON.stringify(zipkinSpans);
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug("Zipkin request payload: " + payload);
        req.write(payload, 'utf8');
        req.end();
    };
} //# sourceMappingURL=util.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/types.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ /**
 * When present, kind clarifies timestamp, duration and remoteEndpoint. When
 * absent, the span is local or incomplete. Unlike client and server, there
 * is no direct critical path latency relationship between producer and
 * consumer spans.
 * `CLIENT`
 *   timestamp is the moment a request was sent to the server.
 *   duration is the delay until a response or an error was received.
 *   remoteEndpoint is the server.
 * `SERVER`
 *   timestamp is the moment a client request was received.
 *   duration is the delay until a response was sent or an error.
 *   remoteEndpoint is the client.
 * `PRODUCER`
 *   timestamp is the moment a message was sent to a destination.
 *   duration is the delay sending the message, such as batching.
 *   remoteEndpoint is the broker.
 * `CONSUMER`
 *   timestamp is the moment a message was received from an origin.
 *   duration is the delay consuming the message, such as from backlog.
 *   remoteEndpoint - Represents the broker. Leave serviceName absent if unknown.
 */ __turbopack_context__.s({
    "SpanKind": (()=>SpanKind)
});
var SpanKind;
(function(SpanKind) {
    SpanKind["CLIENT"] = "CLIENT";
    SpanKind["SERVER"] = "SERVER";
    SpanKind["CONSUMER"] = "CONSUMER";
    SpanKind["PRODUCER"] = "PRODUCER";
})(SpanKind || (SpanKind = {})); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/transform.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "_toZipkinAnnotations": (()=>_toZipkinAnnotations),
    "_toZipkinTags": (()=>_toZipkinTags),
    "defaultStatusCodeTagName": (()=>defaultStatusCodeTagName),
    "defaultStatusErrorTagName": (()=>defaultStatusErrorTagName),
    "toZipkinSpan": (()=>toZipkinSpan)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/common/time.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/types.js [app-rsc] (ecmascript)");
var __values = this && this.__values || function(o) {
    var s = typeof Symbol === "function" && Symbol.iterator, m = s && o[s], i = 0;
    if (m) return m.call(o);
    if (o && typeof o.length === "number") return {
        next: function() {
            if (o && i >= o.length) o = void 0;
            return {
                value: o && o[i++],
                done: !o
            };
        }
    };
    throw new TypeError(s ? "Object is not iterable." : "Symbol.iterator is not defined.");
};
var _a;
;
;
;
var ZIPKIN_SPAN_KIND_MAPPING = (_a = {}, _a[__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanKind"].CLIENT] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SpanKind"].CLIENT, _a[__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanKind"].SERVER] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SpanKind"].SERVER, _a[__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanKind"].CONSUMER] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SpanKind"].CONSUMER, _a[__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanKind"].PRODUCER] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SpanKind"].PRODUCER, // When absent, the span is local.
_a[__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanKind"].INTERNAL] = undefined, _a);
var defaultStatusCodeTagName = 'otel.status_code';
var defaultStatusErrorTagName = 'error';
function toZipkinSpan(span, serviceName, statusCodeTagName, statusErrorTagName) {
    var zipkinSpan = {
        traceId: span.spanContext().traceId,
        parentId: span.parentSpanId,
        name: span.name,
        id: span.spanContext().spanId,
        kind: ZIPKIN_SPAN_KIND_MAPPING[span.kind],
        timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hrTimeToMicroseconds"])(span.startTime),
        duration: Math.round((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hrTimeToMicroseconds"])(span.duration)),
        localEndpoint: {
            serviceName: serviceName
        },
        tags: _toZipkinTags(span, statusCodeTagName, statusErrorTagName),
        annotations: span.events.length ? _toZipkinAnnotations(span.events) : undefined
    };
    return zipkinSpan;
}
function _toZipkinTags(_a, statusCodeTagName, statusErrorTagName) {
    var e_1, _b;
    var attributes = _a.attributes, resource = _a.resource, status = _a.status, droppedAttributesCount = _a.droppedAttributesCount, droppedEventsCount = _a.droppedEventsCount, droppedLinksCount = _a.droppedLinksCount;
    var tags = {};
    try {
        for(var _c = __values(Object.keys(attributes)), _d = _c.next(); !_d.done; _d = _c.next()){
            var key = _d.value;
            tags[key] = String(attributes[key]);
        }
    } catch (e_1_1) {
        e_1 = {
            error: e_1_1
        };
    } finally{
        try {
            if (_d && !_d.done && (_b = _c.return)) _b.call(_c);
        } finally{
            if (e_1) throw e_1.error;
        }
    }
    if (status.code !== __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanStatusCode"].UNSET) {
        tags[statusCodeTagName] = String(__TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanStatusCode"][status.code]);
    }
    if (status.code === __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["SpanStatusCode"].ERROR && status.message) {
        tags[statusErrorTagName] = status.message;
    }
    /* Add droppedAttributesCount as a tag */ if (droppedAttributesCount) {
        tags['otel.dropped_attributes_count'] = String(droppedAttributesCount);
    }
    /* Add droppedEventsCount as a tag */ if (droppedEventsCount) {
        tags['otel.dropped_events_count'] = String(droppedEventsCount);
    }
    /* Add droppedLinksCount as a tag */ if (droppedLinksCount) {
        tags['otel.dropped_links_count'] = String(droppedLinksCount);
    }
    Object.keys(resource.attributes).forEach(function(name) {
        return tags[name] = String(resource.attributes[name]);
    });
    return tags;
}
function _toZipkinAnnotations(events) {
    return events.map(function(event) {
        return {
            timestamp: Math.round((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$common$2f$time$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["hrTimeToMicroseconds"])(event.time)),
            value: event.name
        };
    });
} //# sourceMappingURL=transform.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/utils.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareGetHeaders": (()=>prepareGetHeaders)
});
function prepareGetHeaders(getExportRequestHeaders) {
    return function() {
        return getExportRequestHeaders();
    };
} //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/zipkin.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ __turbopack_context__.s({
    "ZipkinExporter": (()=>ZipkinExporter)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/ExportResult.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/core/build/esm/platform/node/environment.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/node/util.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$transform$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/transform.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$resource$2f$SemanticResourceAttributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/node_modules/@opentelemetry/semantic-conventions/build/esm/resource/SemanticResourceAttributes.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/utils.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
/**
 * Zipkin Exporter
 */ var ZipkinExporter = function() {
    function ZipkinExporter(config) {
        if (config === void 0) {
            config = {};
        }
        this.DEFAULT_SERVICE_NAME = 'OpenTelemetry Service';
        this._sendingPromises = [];
        this._urlStr = config.url || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$platform$2f$node$2f$environment$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getEnv"])().OTEL_EXPORTER_ZIPKIN_ENDPOINT;
        this._send = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareSend"])(this._urlStr, config.headers);
        this._serviceName = config.serviceName;
        this._statusCodeTagName = config.statusCodeTagName || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$transform$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defaultStatusCodeTagName"];
        this._statusDescriptionTagName = config.statusDescriptionTagName || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$transform$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defaultStatusErrorTagName"];
        this._isShutdown = false;
        if (typeof config.getExportRequestHeaders === 'function') {
            this._getHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareGetHeaders"])(config.getExportRequestHeaders);
        } else {
            // noop
            this._beforeSend = function() {};
        }
    }
    /**
     * Export spans.
     */ ZipkinExporter.prototype.export = function(spans, resultCallback) {
        var _this = this;
        var serviceName = String(this._serviceName || spans[0].resource.attributes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$resource$2f$SemanticResourceAttributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEMRESATTRS_SERVICE_NAME"]] || this.DEFAULT_SERVICE_NAME);
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('Zipkin exporter export');
        if (this._isShutdown) {
            setTimeout(function() {
                return resultCallback({
                    code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$core$2f$build$2f$esm$2f$ExportResult$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ExportResultCode"].FAILED,
                    error: new Error('Exporter has been shutdown')
                });
            });
            return;
        }
        var promise = new Promise(function(resolve) {
            _this._sendSpans(spans, serviceName, function(result) {
                resolve();
                resultCallback(result);
            });
        });
        this._sendingPromises.push(promise);
        var popPromise = function() {
            var index = _this._sendingPromises.indexOf(promise);
            _this._sendingPromises.splice(index, 1);
        };
        promise.then(popPromise, popPromise);
    };
    /**
     * Shutdown exporter. Noop operation in this exporter.
     */ ZipkinExporter.prototype.shutdown = function() {
        __TURBOPACK__imported__module__$5b$externals$5d2f40$opentelemetry$2f$api__$5b$external$5d$__$2840$opentelemetry$2f$api$2c$__cjs$29$__["diag"].debug('Zipkin exporter shutdown');
        this._isShutdown = true;
        return this.forceFlush();
    };
    /**
     * Exports any pending spans in exporter
     */ ZipkinExporter.prototype.forceFlush = function() {
        var _this = this;
        return new Promise(function(resolve, reject) {
            Promise.all(_this._sendingPromises).then(function() {
                resolve();
            }, reject);
        });
    };
    /**
     * if user defines getExportRequestHeaders in config then this will be called
     * every time before send, otherwise it will be replaced with noop in
     * constructor
     * @default noop
     */ ZipkinExporter.prototype._beforeSend = function() {
        if (this._getHeaders) {
            this._send = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$node$2f$util$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareSend"])(this._urlStr, this._getHeaders());
        }
    };
    /**
     * Transform spans and sends to Zipkin service.
     */ ZipkinExporter.prototype._sendSpans = function(spans, serviceName, done) {
        var _this = this;
        var zipkinSpans = spans.map(function(span) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$transform$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["toZipkinSpan"])(span, String(span.attributes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$resource$2f$SemanticResourceAttributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEMRESATTRS_SERVICE_NAME"]] || span.resource.attributes[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$node_modules$2f40$opentelemetry$2f$semantic$2d$conventions$2f$build$2f$esm$2f$resource$2f$SemanticResourceAttributes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SEMRESATTRS_SERVICE_NAME"]] || serviceName), _this._statusCodeTagName, _this._statusDescriptionTagName);
        });
        this._beforeSend();
        return this._send(zipkinSpans, function(result) {
            if (done) {
                return done(result);
            }
        });
    };
    return ZipkinExporter;
}();
;
 //# sourceMappingURL=zipkin.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ZipkinExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$zipkin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ZipkinExporter"]),
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$platform$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/platform/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$zipkin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/zipkin.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ZipkinExporter": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ZipkinExporter"]),
    "prepareSend": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["prepareSend"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$opentelemetry$2f$exporter$2d$zipkin$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript) <exports>");
}}),
"[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/types.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ThriftReferenceType = exports.HTTPSender = exports.ThriftUtils = exports.Utils = exports.UDPSender = void 0;
// Below require is needed as jaeger-client types does not expose the thrift,
// udp_sender, util etc. modules.
/* eslint-disable @typescript-eslint/no-var-requires */ exports.UDPSender = __turbopack_context__.r("[project]/node_modules/jaeger-client/dist/src/reporters/udp_sender.js [app-rsc] (ecmascript)").default;
exports.Utils = __turbopack_context__.r("[project]/node_modules/jaeger-client/dist/src/util.js [app-rsc] (ecmascript)").default;
exports.ThriftUtils = __turbopack_context__.r("[project]/node_modules/jaeger-client/dist/src/thrift.js [app-rsc] (ecmascript)").default;
exports.HTTPSender = __turbopack_context__.r("[project]/node_modules/jaeger-client/dist/src/reporters/http_sender.js [app-rsc] (ecmascript)").default;
var ThriftReferenceType;
(function(ThriftReferenceType) {
    ThriftReferenceType["CHILD_OF"] = "CHILD_OF";
    ThriftReferenceType["FOLLOWS_FROM"] = "FOLLOWS_FROM";
})(ThriftReferenceType = exports.ThriftReferenceType || (exports.ThriftReferenceType = {})); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/transform.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.spanToThrift = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const types_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/types.js [app-rsc] (ecmascript)");
const DEFAULT_FLAGS = 0x1;
/**
 * Translate OpenTelemetry ReadableSpan to Jaeger Thrift Span
 * @param span Span to be translated
 */ function spanToThrift(span) {
    const traceId = span.spanContext().traceId.padStart(32, '0');
    const traceIdHigh = traceId.slice(0, 16);
    const traceIdLow = traceId.slice(16);
    const parentSpan = span.parentSpanId ? types_1.Utils.encodeInt64(span.parentSpanId) : types_1.ThriftUtils.emptyBuffer;
    const tags = Object.keys(span.attributes).map((name)=>({
            key: name,
            value: toTagValue(span.attributes[name])
        }));
    if (span.status.code !== api_1.SpanStatusCode.UNSET) {
        tags.push({
            key: 'otel.status_code',
            value: api_1.SpanStatusCode[span.status.code]
        });
        if (span.status.message) {
            tags.push({
                key: 'otel.status_description',
                value: span.status.message
            });
        }
    }
    // Ensure that if SpanStatus.Code is ERROR, that we set the "error" tag on the
    // Jaeger span.
    if (span.status.code === api_1.SpanStatusCode.ERROR) {
        tags.push({
            key: 'error',
            value: true
        });
    }
    if (span.kind !== undefined && span.kind !== api_1.SpanKind.INTERNAL) {
        tags.push({
            key: 'span.kind',
            value: api_1.SpanKind[span.kind].toLowerCase()
        });
    }
    Object.keys(span.resource.attributes).forEach((name)=>tags.push({
            key: name,
            value: toTagValue(span.resource.attributes[name])
        }));
    if (span.instrumentationLibrary) {
        tags.push({
            key: 'otel.library.name',
            value: toTagValue(span.instrumentationLibrary.name)
        });
        tags.push({
            key: 'otel.library.version',
            value: toTagValue(span.instrumentationLibrary.version)
        });
    }
    /* Add droppedAttributesCount as a tag */ if (span.droppedAttributesCount) {
        tags.push({
            key: 'otel.dropped_attributes_count',
            value: toTagValue(span.droppedAttributesCount)
        });
    }
    /* Add droppedEventsCount as a tag */ if (span.droppedEventsCount) {
        tags.push({
            key: 'otel.dropped_events_count',
            value: toTagValue(span.droppedEventsCount)
        });
    }
    /* Add droppedLinksCount as a tag */ if (span.droppedLinksCount) {
        tags.push({
            key: 'otel.dropped_links_count',
            value: toTagValue(span.droppedLinksCount)
        });
    }
    const spanTags = types_1.ThriftUtils.getThriftTags(tags);
    const logs = span.events.map((event)=>{
        const fields = [
            {
                key: 'event',
                value: event.name
            }
        ];
        const attrs = event.attributes;
        if (attrs) {
            Object.keys(attrs).forEach((attr)=>fields.push({
                    key: attr,
                    value: toTagValue(attrs[attr])
                }));
        }
        if (event.droppedAttributesCount) {
            fields.push({
                key: 'otel.event.dropped_attributes_count',
                value: event.droppedAttributesCount
            });
        }
        return {
            timestamp: (0, core_1.hrTimeToMilliseconds)(event.time),
            fields
        };
    });
    const spanLogs = types_1.ThriftUtils.getThriftLogs(logs);
    return {
        traceIdLow: types_1.Utils.encodeInt64(traceIdLow),
        traceIdHigh: types_1.Utils.encodeInt64(traceIdHigh),
        spanId: types_1.Utils.encodeInt64(span.spanContext().spanId),
        parentSpanId: parentSpan,
        operationName: span.name,
        references: spanLinksToThriftRefs(span.links),
        flags: span.spanContext().traceFlags || DEFAULT_FLAGS,
        startTime: types_1.Utils.encodeInt64((0, core_1.hrTimeToMicroseconds)(span.startTime)),
        duration: types_1.Utils.encodeInt64((0, core_1.hrTimeToMicroseconds)(span.duration)),
        tags: spanTags,
        logs: spanLogs
    };
}
exports.spanToThrift = spanToThrift;
/** Translate OpenTelemetry {@link Link}s to Jaeger ThriftReference. */ function spanLinksToThriftRefs(links) {
    return links.map((link)=>{
        const refType = types_1.ThriftReferenceType.FOLLOWS_FROM;
        const traceId = link.context.traceId;
        const traceIdHigh = types_1.Utils.encodeInt64(traceId.slice(0, 16));
        const traceIdLow = types_1.Utils.encodeInt64(traceId.slice(16));
        const spanId = types_1.Utils.encodeInt64(link.context.spanId);
        return {
            traceIdLow,
            traceIdHigh,
            spanId,
            refType
        };
    });
}
/** Translate OpenTelemetry attribute value to Jaeger TagValue. */ function toTagValue(value) {
    const valueType = typeof value;
    if (valueType === 'boolean') {
        return value;
    } else if (valueType === 'number') {
        return value;
    }
    return String(value);
} //# sourceMappingURL=transform.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/jaeger.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.JaegerExporter = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const dgram_1 = __turbopack_context__.r("[externals]/dgram [external] (dgram, cjs)");
const semantic_conventions_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [app-rsc] (ecmascript)");
const transform_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/transform.js [app-rsc] (ecmascript)");
const jaegerTypes = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/types.js [app-rsc] (ecmascript)");
/**
 * Format and sends span information to Jaeger Exporter.
 *
 * @deprecated Jaeger supports the OpenTelemetry protocol natively
 * (see https://www.jaegertracing.io/docs/1.41/apis/#opentelemetry-protocol-stable).
 * This exporter will not be required by the OpenTelemetry specification starting July 2023, and
 * will not receive any security fixes past March 2024.
 *
 * Please migrate to any of the following packages:
 * - `@opentelemetry/exporter-trace-otlp-proto`
 * - `@opentelemetry/exporter-trace-otlp-grpc`
 * - `@opentelemetry/exporter-trace-otlp-http`
 */ class JaegerExporter {
    constructor(config){
        const localConfig = Object.assign({}, config);
        this._onShutdownFlushTimeout = typeof localConfig.flushTimeout === 'number' ? localConfig.flushTimeout : 2000;
        // https://github.com/jaegertracing/jaeger-client-node#environment-variables
        // By default, the client sends traces via UDP to the agent at localhost:6832. Use OTEL_EXPORTER_JAEGER_AGENT_HOST and
        // JAEGER_AGENT_PORT to send UDP traces to a different host:port. If OTEL_EXPORTER_JAEGER_ENDPOINT is set, the client sends traces
        // to the endpoint via HTTP, making the OTEL_EXPORTER_JAEGER_AGENT_HOST and JAEGER_AGENT_PORT unused. If OTEL_EXPORTER_JAEGER_ENDPOINT is secured,
        // HTTP basic authentication can be performed by setting the OTEL_EXPORTER_JAEGER_USER and OTEL_EXPORTER_JAEGER_PASSWORD environment variables.
        const env = (0, core_1.getEnv)();
        localConfig.endpoint = localConfig.endpoint || env.OTEL_EXPORTER_JAEGER_ENDPOINT;
        localConfig.username = localConfig.username || env.OTEL_EXPORTER_JAEGER_USER;
        localConfig.password = localConfig.password || env.OTEL_EXPORTER_JAEGER_PASSWORD;
        localConfig.host = localConfig.host || env.OTEL_EXPORTER_JAEGER_AGENT_HOST;
        localConfig.port = localConfig.port || env.OTEL_EXPORTER_JAEGER_AGENT_PORT;
        this._localConfig = localConfig;
        this._shutdownOnce = new core_1.BindOnceFuture(this._shutdown, this);
    }
    /** Exports a list of spans to Jaeger. */ export(spans, resultCallback) {
        if (this._shutdownOnce.isCalled) {
            return;
        }
        if (spans.length === 0) {
            return resultCallback({
                code: core_1.ExportResultCode.SUCCESS
            });
        }
        api_1.diag.debug('Jaeger exporter export');
        this._sendSpans(spans, resultCallback).catch((error)=>{
            return resultCallback({
                code: core_1.ExportResultCode.FAILED,
                error
            });
        });
    }
    /** Shutdown exporter. */ shutdown() {
        return this._shutdownOnce.call();
    }
    /**
     * Exports any pending spans in exporter
     */ forceFlush() {
        return this._flush();
    }
    _shutdown() {
        return Promise.race([
            new Promise((_resolve, reject)=>{
                setTimeout(()=>reject(new Error('Flush timeout')), this._onShutdownFlushTimeout);
            }),
            this._flush()
        ]).finally(()=>{
            var _a;
            (_a = this._sender) === null || _a === void 0 ? void 0 : _a.close();
        });
    }
    /** Transform spans and sends to Jaeger service. */ async _sendSpans(spans, done) {
        const thriftSpan = spans.map((span)=>(0, transform_1.spanToThrift)(span));
        for (const span of thriftSpan){
            try {
                await this._append(span);
            } catch (error) {
                // TODO right now we break out on first error, is that desirable?
                if (done) return done({
                    code: core_1.ExportResultCode.FAILED,
                    error
                });
            }
        }
        api_1.diag.debug(`successful append for : ${thriftSpan.length}`);
        // Flush all spans on each export. No-op if span buffer is empty
        await this._flush();
        if (done) return process.nextTick(done, {
            code: core_1.ExportResultCode.SUCCESS
        });
    }
    async _append(span) {
        return new Promise((resolve, reject)=>{
            this._getSender(span).append(span, (count, err)=>{
                if (err) {
                    return reject(new Error(err));
                }
                resolve(count);
            });
        });
    }
    _getSender(span) {
        if (this._sender) {
            return this._sender;
        }
        const sender = this._localConfig.endpoint ? new jaegerTypes.HTTPSender(this._localConfig) : new jaegerTypes.UDPSender(this._localConfig);
        if (sender._client instanceof dgram_1.Socket) {
            // unref socket to prevent it from keeping the process running
            sender._client.unref();
        }
        const serviceNameTag = span.tags.find((t)=>t.key === semantic_conventions_1.SEMRESATTRS_SERVICE_NAME);
        const serviceName = (serviceNameTag === null || serviceNameTag === void 0 ? void 0 : serviceNameTag.vStr) || 'unknown_service';
        sender.setProcess({
            serviceName,
            tags: jaegerTypes.ThriftUtils.getThriftTags(this._localConfig.tags || [])
        });
        this._sender = sender;
        return sender;
    }
    async _flush() {
        await new Promise((resolve, reject)=>{
            if (!this._sender) {
                return resolve();
            }
            this._sender.flush((_count, err)=>{
                if (err) {
                    return reject(new Error(err));
                }
                api_1.diag.debug(`successful flush for ${_count} spans`);
                resolve();
            });
        });
    }
}
exports.JaegerExporter = JaegerExporter; //# sourceMappingURL=jaeger.js.map
}}),
"[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.JaegerExporter = void 0;
var jaeger_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/jaeger.js [app-rsc] (ecmascript)");
Object.defineProperty(exports, "JaegerExporter", {
    enumerable: true,
    get: function() {
        return jaeger_1.JaegerExporter;
    }
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var _a;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.TracerProviderWithEnvExporters = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_trace_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_trace_node_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/index.js [app-rsc] (ecmascript)");
const exporter_trace_otlp_proto_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-trace-otlp-proto/build/esm/index.js [app-rsc] (ecmascript)");
const exporter_trace_otlp_http_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-trace-otlp-http/build/esm/index.js [app-rsc] (ecmascript)");
const exporter_trace_otlp_grpc_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-trace-otlp-grpc/build/src/index.js [app-rsc] (ecmascript)");
const exporter_zipkin_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-zipkin/build/esm/index.js [app-rsc] (ecmascript)");
class TracerProviderWithEnvExporters extends sdk_trace_node_1.NodeTracerProvider {
    constructor(config = {}){
        super(config);
        this._configuredExporters = [];
        this._hasSpanProcessors = false;
        let traceExportersList = this.filterBlanksAndNulls(Array.from(new Set((0, core_1.getEnv)().OTEL_TRACES_EXPORTER.split(','))));
        if (traceExportersList[0] === 'none') {
            api_1.diag.warn('OTEL_TRACES_EXPORTER contains "none". SDK will not be initialized.');
        } else if (traceExportersList.length === 0) {
            api_1.diag.warn('OTEL_TRACES_EXPORTER is empty. Using default otlp exporter.');
            traceExportersList = [
                'otlp'
            ];
            this.createExportersFromList(traceExportersList);
            this._spanProcessors = this.configureSpanProcessors(this._configuredExporters);
            this._spanProcessors.forEach((processor)=>{
                this.addSpanProcessor(processor);
            });
        } else {
            if (traceExportersList.length > 1 && traceExportersList.includes('none')) {
                api_1.diag.warn('OTEL_TRACES_EXPORTER contains "none" along with other exporters. Using default otlp exporter.');
                traceExportersList = [
                    'otlp'
                ];
            }
            this.createExportersFromList(traceExportersList);
            if (this._configuredExporters.length > 0) {
                this._spanProcessors = this.configureSpanProcessors(this._configuredExporters);
                this._spanProcessors.forEach((processor)=>{
                    this.addSpanProcessor(processor);
                });
            } else {
                api_1.diag.warn('Unable to set up trace exporter(s) due to invalid exporter and/or protocol values.');
            }
        }
    }
    static configureOtlp() {
        const protocol = this.getOtlpProtocol();
        switch(protocol){
            case 'grpc':
                return new exporter_trace_otlp_grpc_1.OTLPTraceExporter();
            case 'http/json':
                return new exporter_trace_otlp_http_1.OTLPTraceExporter();
            case 'http/protobuf':
                return new exporter_trace_otlp_proto_1.OTLPTraceExporter();
            default:
                api_1.diag.warn(`Unsupported OTLP traces protocol: ${protocol}. Using http/protobuf.`);
                return new exporter_trace_otlp_proto_1.OTLPTraceExporter();
        }
    }
    static getOtlpProtocol() {
        var _b, _c, _d;
        const parsedEnvValues = (0, core_1.getEnvWithoutDefaults)();
        return (_d = (_c = (_b = parsedEnvValues.OTEL_EXPORTER_OTLP_TRACES_PROTOCOL) !== null && _b !== void 0 ? _b : parsedEnvValues.OTEL_EXPORTER_OTLP_PROTOCOL) !== null && _c !== void 0 ? _c : (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_TRACES_PROTOCOL) !== null && _d !== void 0 ? _d : (0, core_1.getEnv)().OTEL_EXPORTER_OTLP_PROTOCOL;
    }
    static configureJaeger() {
        // The JaegerExporter does not support being required in bundled
        // environments. By delaying the require statement to here, we only crash when
        // the exporter is actually used in such an environment.
        try {
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const { JaegerExporter } = __turbopack_context__.r("[project]/node_modules/@opentelemetry/exporter-jaeger/build/src/index.js [app-rsc] (ecmascript)");
            return new JaegerExporter();
        } catch (e) {
            throw new Error(`Could not instantiate JaegerExporter. This could be due to the JaegerExporter's lack of support for bundling. If possible, use @opentelemetry/exporter-trace-otlp-proto instead. Original Error: ${e}`);
        }
    }
    addSpanProcessor(spanProcessor) {
        super.addSpanProcessor(spanProcessor);
        this._hasSpanProcessors = true;
    }
    register(config) {
        if (this._hasSpanProcessors) {
            super.register(config);
        }
    }
    createExportersFromList(exporterList) {
        exporterList.forEach((exporterName)=>{
            const exporter = this._getSpanExporter(exporterName);
            if (exporter) {
                this._configuredExporters.push(exporter);
            } else {
                api_1.diag.warn(`Unrecognized OTEL_TRACES_EXPORTER value: ${exporterName}.`);
            }
        });
    }
    configureSpanProcessors(exporters) {
        return exporters.map((exporter)=>{
            if (exporter instanceof sdk_trace_base_1.ConsoleSpanExporter) {
                return new sdk_trace_base_1.SimpleSpanProcessor(exporter);
            } else {
                return new sdk_trace_base_1.BatchSpanProcessor(exporter);
            }
        });
    }
    filterBlanksAndNulls(list) {
        return list.map((item)=>item.trim()).filter((s)=>s !== 'null' && s !== '');
    }
}
exports.TracerProviderWithEnvExporters = TracerProviderWithEnvExporters;
_a = TracerProviderWithEnvExporters;
TracerProviderWithEnvExporters._registeredExporters = new Map([
    [
        'otlp',
        ()=>_a.configureOtlp()
    ],
    [
        'zipkin',
        ()=>new exporter_zipkin_1.ZipkinExporter()
    ],
    [
        'jaeger',
        ()=>_a.configureJaeger()
    ],
    [
        'console',
        ()=>new sdk_trace_base_1.ConsoleSpanExporter()
    ]
]); //# sourceMappingURL=TracerProviderWithEnvExporter.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-node/build/src/utils.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getResourceDetectorsFromEnv = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const resources_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/resources/build/esm/index.js [app-rsc] (ecmascript)");
const RESOURCE_DETECTOR_ENVIRONMENT = 'env';
const RESOURCE_DETECTOR_HOST = 'host';
const RESOURCE_DETECTOR_OS = 'os';
const RESOURCE_DETECTOR_PROCESS = 'process';
const RESOURCE_DETECTOR_SERVICE_INSTANCE_ID = 'serviceinstance';
function getResourceDetectorsFromEnv() {
    var _a, _b;
    // When updating this list, make sure to also update the section `resourceDetectors` on README.
    const resourceDetectors = new Map([
        [
            RESOURCE_DETECTOR_ENVIRONMENT,
            resources_1.envDetectorSync
        ],
        [
            RESOURCE_DETECTOR_HOST,
            resources_1.hostDetectorSync
        ],
        [
            RESOURCE_DETECTOR_OS,
            resources_1.osDetectorSync
        ],
        [
            RESOURCE_DETECTOR_SERVICE_INSTANCE_ID,
            resources_1.serviceInstanceIdDetectorSync
        ],
        [
            RESOURCE_DETECTOR_PROCESS,
            resources_1.processDetectorSync
        ]
    ]);
    const resourceDetectorsFromEnv = (_b = (_a = process.env.OTEL_NODE_RESOURCE_DETECTORS) === null || _a === void 0 ? void 0 : _a.split(',')) !== null && _b !== void 0 ? _b : [
        'all'
    ];
    if (resourceDetectorsFromEnv.includes('all')) {
        return [
            ...resourceDetectors.values()
        ].flat();
    }
    if (resourceDetectorsFromEnv.includes('none')) {
        return [];
    }
    return resourceDetectorsFromEnv.flatMap((detector)=>{
        const resourceDetector = resourceDetectors.get(detector);
        if (!resourceDetector) {
            api_1.diag.error(`Invalid resource detector "${detector}" specified in the environment variable OTEL_NODE_RESOURCE_DETECTORS`);
        }
        return resourceDetector || [];
    });
}
exports.getResourceDetectorsFromEnv = getResourceDetectorsFromEnv; //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-node/build/src/sdk.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.NodeSDK = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const api_logs_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/api-logs/build/esm/index.js [app-rsc] (ecmascript)");
const instrumentation_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/instrumentation/build/esm/index.js [app-rsc] (ecmascript)");
const resources_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/resources/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_logs_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_metrics_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-metrics/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_trace_base_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)");
const sdk_trace_node_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/index.js [app-rsc] (ecmascript)");
const semantic_conventions_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/node_modules/@opentelemetry/semantic-conventions/build/esm/index.js [app-rsc] (ecmascript)");
const TracerProviderWithEnvExporter_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js [app-rsc] (ecmascript)");
const core_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
const utils_1 = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/build/src/utils.js [app-rsc] (ecmascript)");
class NodeSDK {
    /**
     * Create a new NodeJS SDK instance
     */ constructor(configuration = {}){
        var _a, _b, _c, _d, _e, _f, _g;
        const env = (0, core_1.getEnv)();
        const envWithoutDefaults = (0, core_1.getEnvWithoutDefaults)();
        if (env.OTEL_SDK_DISABLED) {
            this._disabled = true;
        // Functions with possible side-effects are set
        // to no-op via the _disabled flag
        }
        // Default is INFO, use environment without defaults to check
        // if the user originally set the environment variable.
        if (envWithoutDefaults.OTEL_LOG_LEVEL) {
            api_1.diag.setLogger(new api_1.DiagConsoleLogger(), {
                logLevel: envWithoutDefaults.OTEL_LOG_LEVEL
            });
        }
        this._configuration = configuration;
        this._resource = (_a = configuration.resource) !== null && _a !== void 0 ? _a : new resources_1.Resource({});
        let defaultDetectors = [];
        if (process.env.OTEL_NODE_RESOURCE_DETECTORS != null) {
            defaultDetectors = (0, utils_1.getResourceDetectorsFromEnv)();
        } else {
            defaultDetectors = [
                resources_1.envDetector,
                resources_1.processDetector,
                resources_1.hostDetector
            ];
        }
        this._resourceDetectors = (_b = configuration.resourceDetectors) !== null && _b !== void 0 ? _b : defaultDetectors;
        this._serviceName = configuration.serviceName;
        this._autoDetectResources = (_c = configuration.autoDetectResources) !== null && _c !== void 0 ? _c : true;
        // If a tracer provider can be created from manual configuration, create it
        if (configuration.traceExporter || configuration.spanProcessor || configuration.spanProcessors) {
            const tracerProviderConfig = {};
            if (configuration.sampler) {
                tracerProviderConfig.sampler = configuration.sampler;
            }
            if (configuration.spanLimits) {
                tracerProviderConfig.spanLimits = configuration.spanLimits;
            }
            if (configuration.idGenerator) {
                tracerProviderConfig.idGenerator = configuration.idGenerator;
            }
            if (configuration.spanProcessor) {
                api_1.diag.warn("The 'spanProcessor' option is deprecated. Please use 'spanProcessors' instead.");
            }
            const spanProcessor = (_d = configuration.spanProcessor) !== null && _d !== void 0 ? _d : // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            new sdk_trace_base_1.BatchSpanProcessor(configuration.traceExporter);
            const spanProcessors = (_e = configuration.spanProcessors) !== null && _e !== void 0 ? _e : [
                spanProcessor
            ];
            this._tracerProviderConfig = {
                tracerConfig: tracerProviderConfig,
                spanProcessors,
                contextManager: configuration.contextManager,
                textMapPropagator: configuration.textMapPropagator
            };
        }
        if (configuration.logRecordProcessor) {
            this._loggerProviderConfig = {
                logRecordProcessor: configuration.logRecordProcessor
            };
        }
        if (configuration.metricReader || configuration.views) {
            const meterProviderConfig = {};
            if (configuration.metricReader) {
                meterProviderConfig.reader = configuration.metricReader;
            }
            if (configuration.views) {
                meterProviderConfig.views = configuration.views;
            }
            this._meterProviderConfig = meterProviderConfig;
        }
        this._instrumentations = (_g = (_f = configuration.instrumentations) === null || _f === void 0 ? void 0 : _f.flat()) !== null && _g !== void 0 ? _g : [];
    }
    /**
     * Call this method to construct SDK components and register them with the OpenTelemetry API.
     */ start() {
        var _a, _b, _c, _d, _e, _f;
        if (this._disabled) {
            return;
        }
        (0, instrumentation_1.registerInstrumentations)({
            instrumentations: this._instrumentations
        });
        if (this._autoDetectResources) {
            const internalConfig = {
                detectors: this._resourceDetectors
            };
            this._resource = this._resource.merge((0, resources_1.detectResourcesSync)(internalConfig));
        }
        this._resource = this._serviceName === undefined ? this._resource : this._resource.merge(new resources_1.Resource({
            [semantic_conventions_1.SEMRESATTRS_SERVICE_NAME]: this._serviceName
        }));
        // if there is a tracerProviderConfig (traceExporter/spanProcessor was set manually) or the traceExporter is set manually, use NodeTracerProvider
        const Provider = this._tracerProviderConfig ? sdk_trace_node_1.NodeTracerProvider : TracerProviderWithEnvExporter_1.TracerProviderWithEnvExporters;
        // If the Provider is configured with Env Exporters, we need to check if the SDK had any manual configurations and set them here
        const tracerProvider = new Provider(Object.assign(Object.assign({}, this._configuration), {
            resource: this._resource
        }));
        this._tracerProvider = tracerProvider;
        if (this._tracerProviderConfig) {
            for (const spanProcessor of this._tracerProviderConfig.spanProcessors){
                tracerProvider.addSpanProcessor(spanProcessor);
            }
        }
        tracerProvider.register({
            contextManager: (_b = (_a = this._tracerProviderConfig) === null || _a === void 0 ? void 0 : _a.contextManager) !== null && _b !== void 0 ? _b : // _tracerProviderConfig may be undefined if trace-specific settings are not provided - fall back to raw config
            (_c = this._configuration) === null || _c === void 0 ? void 0 : _c.contextManager,
            propagator: (_d = this._tracerProviderConfig) === null || _d === void 0 ? void 0 : _d.textMapPropagator
        });
        if (this._loggerProviderConfig) {
            const loggerProvider = new sdk_logs_1.LoggerProvider({
                resource: this._resource
            });
            loggerProvider.addLogRecordProcessor(this._loggerProviderConfig.logRecordProcessor);
            this._loggerProvider = loggerProvider;
            api_logs_1.logs.setGlobalLoggerProvider(loggerProvider);
        }
        if (this._meterProviderConfig) {
            const readers = [];
            if (this._meterProviderConfig.reader) {
                readers.push(this._meterProviderConfig.reader);
            }
            const meterProvider = new sdk_metrics_1.MeterProvider({
                resource: this._resource,
                views: (_f = (_e = this._meterProviderConfig) === null || _e === void 0 ? void 0 : _e.views) !== null && _f !== void 0 ? _f : [],
                readers: readers
            });
            this._meterProvider = meterProvider;
            api_1.metrics.setGlobalMeterProvider(meterProvider);
            // TODO: This is a workaround to fix https://github.com/open-telemetry/opentelemetry-js/issues/3609
            // If the MeterProvider is not yet registered when instrumentations are registered, all metrics are dropped.
            // This code is obsolete once https://github.com/open-telemetry/opentelemetry-js/issues/3622 is implemented.
            for (const instrumentation of this._instrumentations){
                instrumentation.setMeterProvider(api_1.metrics.getMeterProvider());
            }
        }
    }
    shutdown() {
        const promises = [];
        if (this._tracerProvider) {
            promises.push(this._tracerProvider.shutdown());
        }
        if (this._loggerProvider) {
            promises.push(this._loggerProvider.shutdown());
        }
        if (this._meterProvider) {
            promises.push(this._meterProvider.shutdown());
        }
        return Promise.all(promises)// return void instead of the array from Promise.all
        .then(()=>{});
    }
}
exports.NodeSDK = NodeSDK; //# sourceMappingURL=sdk.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-node/build/src/types.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
}); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@opentelemetry/sdk-node/build/src/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, {
        enumerable: true,
        get: function() {
            return m[k];
        }
    });
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.tracing = exports.resources = exports.node = exports.metrics = exports.logs = exports.core = exports.contextBase = exports.api = void 0;
exports.api = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
exports.contextBase = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
exports.core = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
exports.logs = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-logs/build/esm/index.js [app-rsc] (ecmascript)");
exports.metrics = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-metrics/build/esm/index.js [app-rsc] (ecmascript)");
exports.node = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-node/build/src/index.js [app-rsc] (ecmascript)");
exports.resources = __turbopack_context__.r("[project]/node_modules/@opentelemetry/resources/build/esm/index.js [app-rsc] (ecmascript)");
exports.tracing = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)");
__exportStar(__turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/build/src/sdk.js [app-rsc] (ecmascript)"), exports);
__exportStar(__turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/build/src/types.js [app-rsc] (ecmascript)"), exports); //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=node_modules_%40opentelemetry_26c0981b._.js.map