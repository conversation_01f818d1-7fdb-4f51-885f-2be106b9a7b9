self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40d6d3ae52e39459a461b92189ae613e2eae71f4da\": {\n      \"workers\": {\n        \"app/admin/posts/create/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/admin/posts/create/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/generate-blog-post.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/admin/posts/create/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"BaBSf3PH2BxVxrwhY73k6TCgHy1JX5Mwit6cGAeq7zs=\"\n}"