{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/Header.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON> } from \"lucide-react\";\r\n\r\nexport function Header() {\r\n  return (\r\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\r\n      <div className=\"container flex h-14 items-center\">\r\n        <div className=\"mr-6 flex items-center\">\r\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n            <Rocket className=\"h-6 w-6 text-primary\" />\r\n            <span className=\"font-bold font-headline text-lg\">ArtechAI</span>\r\n          </Link>\r\n        </div>\r\n        <nav className=\"hidden md:flex items-center space-x-6 text-sm font-medium\">\r\n          <Link\r\n            href=\"/\"\r\n            className=\"transition-colors hover:text-primary\"\r\n          >\r\n            Home\r\n          </Link>\r\n          <Link\r\n            href=\"#about\"\r\n            className=\"transition-colors hover:text-primary\"\r\n          >\r\n            About\r\n          </Link>\r\n          <Link\r\n            href=\"#categories\"\r\n            className=\"transition-colors hover:text-primary\"\r\n          >\r\n            Categories\r\n          </Link>\r\n          <Link\r\n            href=\"#featured\"\r\n            className=\"transition-colors hover:text-primary\"\r\n          >\r\n            Featured\r\n          </Link>\r\n        </nav>\r\n        <div className=\"flex flex-1 items-center justify-end space-x-4\">\r\n          <Button asChild>\r\n            <Link href=\"/login\">Admin Login</Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAK,WAAU;0CAAkC;;;;;;;;;;;;;;;;;8BAGtD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;sCAGD,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/Hero.tsx"], "sourcesContent": ["import { Button } from \"@/components/ui/button\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nexport function Hero() {\r\n  return (\r\n    <section className=\"relative w-full h-[60vh] flex items-center justify-center text-center\">\r\n      <Image\r\n        src=\"https://picsum.photos/1200/800\"\r\n        alt=\"Abstract AI background\"\r\n        fill\r\n        className=\"object-cover\"\r\n        data-ai-hint=\"abstract ai\"\r\n        priority\r\n      />\r\n      <div className=\"absolute inset-0 bg-black/60\" />\r\n      <div className=\"relative z-10 max-w-4xl mx-auto px-4 text-white\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold font-headline leading-tight\">\r\n          Artechway: Where Art & Tech Innovation Collide\r\n        </h1>\r\n        <p className=\"mt-4 text-lg md:text-xl text-neutral-200\">\r\n          Your AI-Powered Blog for Art and Tech Innovation. Discover the future,\r\n          today.\r\n        </p>\r\n        <div className=\"mt-8\">\r\n          <Button size=\"lg\" asChild>\r\n            <Link href=\"#featured\">Explore Posts</Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,IAAI;gBACJ,WAAU;gBACV,gBAAa;gBACb,QAAQ;;;;;;0BAEV,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAG3E,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;kCAIxD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAK,OAAO;sCACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/CategoryCard.tsx"], "sourcesContent": ["import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from \"@/components/ui/card\";\r\nimport type { LucideIcon } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface CategoryCardProps {\r\n  title: string;\r\n  icon: LucideIcon;\r\n  className?: string;\r\n}\r\n\r\nexport function CategoryCard({\r\n  title,\r\n  icon: Icon,\r\n  className,\r\n}: CategoryCardProps) {\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        \"text-center transition-all duration-300 hover:scale-105 hover:shadow-lg hover:-translate-y-1 cursor-pointer\",\r\n        className\r\n      )}\r\n    >\r\n      <CardHeader>\r\n        <div className=\"mx-auto bg-primary/10 rounded-full p-3 w-fit\">\r\n          <Icon className=\"h-8 w-8 text-primary\" />\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <CardTitle className=\"text-lg font-headline\">{title}</CardTitle>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAQO,SAAS,aAAa,EAC3B,KAAK,EACL,MAAM,IAAI,EACV,SAAS,EACS;IAClB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+GACA;;0BAGF,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;;;;;;;;;;;;;;;;0BAGpB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAItD", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/BlogPostCard.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport {\r\n  <PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n} from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\ninterface BlogPostCardProps {\r\n  imageUrl: string;\r\n  title: string;\r\n  summary: string;\r\n  imageHint: string;\r\n  slug: string;\r\n}\r\n\r\nexport function BlogPostCard({\r\n  imageUrl,\r\n  title,\r\n  summary,\r\n  imageHint,\r\n  slug,\r\n}: BlogPostCardProps) {\r\n  return (\r\n    <Card className=\"overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 flex flex-col\">\r\n      <CardHeader className=\"p-0\">\r\n        <div className=\"aspect-video relative\">\r\n          <Image\r\n            src={imageUrl}\r\n            alt={title}\r\n            width={600}\r\n            height={400}\r\n            className=\"object-cover w-full h-full\"\r\n            data-ai-hint={imageHint}\r\n          />\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-6 flex-grow\">\r\n        <CardTitle className=\"text-xl font-headline mb-2\">{title}</CardTitle>\r\n        <p className=\"text-muted-foreground line-clamp-3\">{summary}</p>\r\n      </CardContent>\r\n      <CardFooter>\r\n        <Button variant=\"link\" asChild className=\"text-primary hover:text-accent p-0 font-bold\">\r\n          <Link href={`/blog/${slug}`}>\r\n            Read More <ArrowRight className=\"ml-2 h-4 w-4\" />\r\n          </Link>\r\n        </Button>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;AACA;AACA;;;;;;;AAUO,SAAS,aAAa,EAC3B,QAAQ,EACR,KAAK,EACL,OAAO,EACP,SAAS,EACT,IAAI,EACc;IAClB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,gBAAc;;;;;;;;;;;;;;;;0BAIpB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAA8B;;;;;;kCACnD,8OAAC;wBAAE,WAAU;kCAAsC;;;;;;;;;;;;0BAErD,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAO,OAAO;oBAAC,WAAU;8BACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,MAAM,EAAE,MAAM;;4BAAE;0CACjB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/NewsletterSignup.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\n\r\nexport function NewsletterSignup() {\r\n  return (\r\n    <section className=\"py-16 md:py-24 bg-muted/50\">\r\n      <div className=\"container\">\r\n        <Card className=\"max-w-2xl mx-auto text-center shadow-lg border-primary/20\">\r\n          <CardHeader>\r\n            <CardTitle className=\"text-3xl font-headline\">\r\n              Stay Ahead of the Curve\r\n            </CardTitle>\r\n            <CardDescription>\r\n              Subscribe to our newsletter for the latest in AI, art, and\r\n              technology.\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <form className=\"flex w-full items-center space-x-2\">\r\n              <Input\r\n                type=\"email\"\r\n                placeholder=\"Enter your email\"\r\n                className=\"flex-1\"\r\n              />\r\n              <Button type=\"submit\">Subscribe</Button>\r\n            </form>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAyB;;;;;;0CAG9C,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAKnB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAK,WAAU;;8CACd,8OAAC,iIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/About.tsx"], "sourcesContent": ["export function About() {\r\n  return (\r\n    <section id=\"about\" className=\"py-16 md:py-24\">\r\n      <div className=\"container max-w-4xl mx-auto text-center\">\r\n        <h2 className=\"text-3xl font-bold font-headline tracking-tight sm:text-4xl\">\r\n          About <span className=\"text-primary\">ArtechAI</span>\r\n        </h2>\r\n        <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\r\n          Artechway is your premier destination for exploring the dynamic\r\n          intersection of art and artificial intelligence. We are dedicated to\r\n          bringing you the latest news, insightful analyses, and inspiring\r\n          stories from the world of AI-driven creativity and technological\r\n          innovation. Our mission is to be the leading voice in the conversation\r\n          about the future of art and technology, making complex topics\r\n          accessible and exciting for everyone.\r\n        </p>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;;wBAA8D;sCACpE,8OAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;8BAEvC,8OAAC;oBAAE,WAAU;8BAA+C;;;;;;;;;;;;;;;;;AAYpE", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/Footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { Rocket, Twitter, Linkedin, Github } from \"lucide-react\";\r\n\r\nexport function Footer() {\r\n  return (\r\n    <footer className=\"bg-background border-t\">\r\n      <div className=\"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\">\r\n        <div className=\"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\">\r\n          <Rocket className=\"h-6 w-6 text-primary\" />\r\n          <p className=\"text-center text-sm leading-loose text-muted-foreground md:text-left\">\r\n            © {new Date().getFullYear()} ArtechAI. All rights reserved.\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-4\">\r\n          <Link\r\n            href=\"#\"\r\n            target=\"_blank\"\r\n            rel=\"noreferrer\"\r\n            className=\"text-muted-foreground hover:text-primary\"\r\n          >\r\n            <Twitter className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">Twitter</span>\r\n          </Link>\r\n          <Link\r\n            href=\"#\"\r\n            target=\"_blank\"\r\n            rel=\"noreferrer\"\r\n            className=\"text-muted-foreground hover:text-primary\"\r\n          >\r\n            <Linkedin className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">LinkedIn</span>\r\n          </Link>\r\n          <Link\r\n            href=\"#\"\r\n            target=\"_blank\"\r\n            rel=\"noreferrer\"\r\n            className=\"text-muted-foreground hover:text-primary\"\r\n          >\r\n            <Github className=\"h-5 w-5\" />\r\n            <span className=\"sr-only\">GitHub</span>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;;gCAAuE;gCAC/E,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;8BAGhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,WAAU;;8CAEV,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAE5B,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,WAAU;;8CAEV,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAE5B,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,QAAO;4BACP,KAAI;4BACJ,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC", "debugId": null}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/lib/posts.ts"], "sourcesContent": ["\r\nconst generateSlug = (title: string) => {\r\n  return title\r\n    .toLowerCase()\r\n    .trim()\r\n    .replace(/[^a-z0-9\\s-]/g, \"\")\r\n    .replace(/\\s+/g, \"-\")\r\n    .replace(/-+/g, \"-\");\r\n};\r\n\r\nexport const posts = [\r\n  {\r\n    slug: generateSlug(\"The Intersection of Generative AI and Modern Art\"),\r\n    imageUrl: \"https://picsum.photos/600/400\",\r\n    imageHint: \"abstract art\",\r\n    title: \"The Intersection of Generative AI and Modern Art\",\r\n    summary:\r\n      \"Exploring how new AI models are revolutionizing the art world, creating pieces that challenge our perception of creativity.\",\r\n    content: \"Full blog post content goes here. This is a longer version of the summary, diving deep into the intersection of generative AI and modern art. We'll explore various AI models, their impact on artistic styles, and the philosophical questions that arise. We'll also showcase some stunning examples of AI-generated art. The world of art is changing, and AI is at the forefront of this revolution. Join us as we explore this exciting new frontier and what it means for artists and art lovers alike.\",\r\n    tags: \"Generative AI, Modern Art, Creativity\",\r\n    date: \"2023-10-27\",\r\n    category: \"AI Design\",\r\n    status: \"Published\",\r\n  },\r\n  {\r\n    slug: generateSlug(\"AI in Business: A Guide to Boosting Productivity\"),\r\n    imageUrl: \"https://picsum.photos/601/400\",\r\n    imageHint: \"business meeting\",\r\n    title: \"AI in Business: A Guide to Boosting Productivity\",\r\n    summary:\r\n      \"Discover practical ways to integrate AI tools into your business operations for enhanced efficiency and decision-making.\",\r\n    content: \"This comprehensive guide will walk you through the various ways AI can boost productivity in your business. From automating repetitive tasks to providing deep data insights, AI is a game-changer. We'll cover specific tools and strategies you can implement today to start seeing results. Whether you're a small startup or a large corporation, this post will provide valuable information to help you leverage the power of AI.\",\r\n    tags: \"AI, Business, Productivity, Technology\",\r\n    date: \"2023-10-24\",\r\n    category: \"AI for Business\",\r\n    status: \"Published\",\r\n  },\r\n  {\r\n    slug: generateSlug(\"What's Next for AI? Predictions for the Coming Decade\"),\r\n    imageUrl: \"https://picsum.photos/600/401\",\r\n    imageHint: \"futuristic technology\",\r\n    title: \"What's Next for AI? Predictions for the Coming Decade\",\r\n    summary:\r\n      \"A deep dive into the trends and breakthroughs that will shape the future of artificial intelligence and its impact on society.\",\r\n    content: \"Artificial intelligence is evolving at an exponential rate. In this post, we'll look at the most promising trends and make some bold predictions for the next ten years. From advancements in natural language processing to the rise of autonomous systems, we'll cover the technologies that are set to redefine our world. What will society look like in 2033? This post explores the possibilities.\",\r\n    tags: \"AI, Future, Technology, Predictions\",\r\n    date: \"2023-10-21\",\r\n    category: \"Future of AI\",\r\n    status: \"Published\",\r\n  },\r\n  {\r\n    slug: generateSlug(\"The Ethics of AI-Generated Content\"),\r\n    imageUrl: \"https://picsum.photos/601/401\",\r\n    imageHint: \"scales of justice\",\r\n    title: \"The Ethics of AI-Generated Content\",\r\n    summary:\r\n      \"As AI becomes more capable of creating content, what are the ethical implications we need to consider?\",\r\n    content: \"The rise of AI-generated content raises a host of ethical questions. Who owns the copyright to AI art? How do we combat misinformation when fake text and images are so easy to create? This article delves into the complex ethical landscape of AI content generation. We will discuss the challenges and potential solutions, and why it's crucial to have these conversations now.\",\r\n    tags: \"AI, Ethics, Content Creation, Misinformation\",\r\n    date: \"2023-10-19\",\r\n    category: \"Future of AI\",\r\n    status: \"Draft\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AACA,MAAM,eAAe,CAAC;IACpB,OAAO,MACJ,WAAW,GACX,IAAI,GACJ,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO;AACpB;AAEO,MAAM,QAAQ;IACnB;QACE,MAAM,aAAa;QACnB,UAAU;QACV,WAAW;QACX,OAAO;QACP,SACE;QACF,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,MAAM,aAAa;QACnB,UAAU;QACV,WAAW;QACX,OAAO;QACP,SACE;QACF,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,MAAM,aAAa;QACnB,UAAU;QACV,WAAW;QACX,OAAO;QACP,SACE;QACF,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,MAAM,aAAa;QACnB,UAAU;QACV,WAAW;QACX,OAAO;QACP,SACE;QACF,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/app/page.tsx"], "sourcesContent": ["import { Header } from \"@/components/Header\";\r\nimport { Hero } from \"@/components/Hero\";\r\nimport { CategoryCard } from \"@/components/CategoryCard\";\r\nimport { BlogPostCard } from \"@/components/BlogPostCard\";\r\nimport { NewsletterSignup } from \"@/components/NewsletterSignup\";\r\nimport { About } from \"@/components/About\";\r\nimport { Footer } from \"@/components/Footer\";\r\nimport {\r\n  Cpu,\r\n  Palette,\r\n  Briefcase,\r\n  Megaphone,\r\n  BrainCircuit,\r\n} from \"lucide-react\";\r\nimport { posts } from \"@/lib/posts\";\r\n\r\nconst categories = [\r\n  { title: \"AI News\", icon: Cpu },\r\n  { title: \"AI Design\", icon: Palette },\r\n  { title: \"AI for Business\", icon: Briefcase },\r\n  { title: \"AI Marketing\", icon: Megaphone },\r\n  { title: \"Future of AI\", icon: BrainCircuit },\r\n];\r\n\r\nconst featuredPosts = posts.slice(0, 3);\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div className=\"flex flex-col min-h-screen\">\r\n      <Header />\r\n      <main className=\"flex-1\">\r\n        <Hero />\r\n\r\n        <section id=\"categories\" className=\"py-16 md:py-24\">\r\n          <div className=\"container\">\r\n            <h2 className=\"text-3xl font-bold text-center font-headline tracking-tight sm:text-4xl\">\r\n              Explore Categories\r\n            </h2>\r\n            <div className=\"mt-12 grid gap-8 md:grid-cols-3 lg:grid-cols-5\">\r\n              {categories.map((category) => (\r\n                <CategoryCard\r\n                  key={category.title}\r\n                  title={category.title}\r\n                  icon={category.icon}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <section id=\"featured\" className=\"py-16 md:py-24 bg-muted/50\">\r\n          <div className=\"container\">\r\n            <h2 className=\"text-3xl font-bold text-center font-headline tracking-tight sm:text-4xl\">\r\n              Featured Posts\r\n            </h2>\r\n            <div className=\"mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3\">\r\n              {featuredPosts.map((post) => (\r\n                <BlogPostCard key={post.slug} {...post} />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n\r\n        <About />\r\n\r\n        <NewsletterSignup />\r\n      </main>\r\n      <Footer />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;;;;;;;;;AAEA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAW,MAAM,gMAAA,CAAA,MAAG;IAAC;IAC9B;QAAE,OAAO;QAAa,MAAM,wMAAA,CAAA,UAAO;IAAC;IACpC;QAAE,OAAO;QAAmB,MAAM,4MAAA,CAAA,YAAS;IAAC;IAC5C;QAAE,OAAO;QAAgB,MAAM,4MAAA,CAAA,YAAS;IAAC;IACzC;QAAE,OAAO;QAAgB,MAAM,sNAAA,CAAA,eAAY;IAAC;CAC7C;AAED,MAAM,gBAAgB,mHAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG;AAEtB,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,SAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,0HAAA,CAAA,OAAI;;;;;kCAEL,8OAAC;wBAAQ,IAAG;wBAAa,WAAU;kCACjC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CAGxF,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,eAAY;4CAEX,OAAO,SAAS,KAAK;4CACrB,MAAM,SAAS,IAAI;2CAFd,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;kCAS7B,8OAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0E;;;;;;8CAGxF,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,kIAAA,CAAA,eAAY;4CAAkB,GAAG,IAAI;2CAAnB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;kCAMpC,8OAAC,2HAAA,CAAA,QAAK;;;;;kCAEN,8OAAC,sIAAA,CAAA,mBAAgB;;;;;;;;;;;0BAEnB,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}