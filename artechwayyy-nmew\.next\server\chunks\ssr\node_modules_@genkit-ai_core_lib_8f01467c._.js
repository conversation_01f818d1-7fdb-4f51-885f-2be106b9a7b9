module.exports = {

"[project]/node_modules/@genkit-ai/core/lib/statusTypes.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var statusTypes_exports = {};
__export(statusTypes_exports, {
    StatusCodes: ()=>StatusCodes,
    StatusNameSchema: ()=>StatusNameSchema,
    StatusSchema: ()=>StatusSchema,
    httpStatusCode: ()=>httpStatusCode
});
module.exports = __toCommonJS(statusTypes_exports);
var z = __toESM(__turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)"));
var StatusCodes = /* @__PURE__ */ ((StatusCodes2)=>{
    StatusCodes2[StatusCodes2["OK"] = 0] = "OK";
    StatusCodes2[StatusCodes2["CANCELLED"] = 1] = "CANCELLED";
    StatusCodes2[StatusCodes2["UNKNOWN"] = 2] = "UNKNOWN";
    StatusCodes2[StatusCodes2["INVALID_ARGUMENT"] = 3] = "INVALID_ARGUMENT";
    StatusCodes2[StatusCodes2["DEADLINE_EXCEEDED"] = 4] = "DEADLINE_EXCEEDED";
    StatusCodes2[StatusCodes2["NOT_FOUND"] = 5] = "NOT_FOUND";
    StatusCodes2[StatusCodes2["ALREADY_EXISTS"] = 6] = "ALREADY_EXISTS";
    StatusCodes2[StatusCodes2["PERMISSION_DENIED"] = 7] = "PERMISSION_DENIED";
    StatusCodes2[StatusCodes2["UNAUTHENTICATED"] = 16] = "UNAUTHENTICATED";
    StatusCodes2[StatusCodes2["RESOURCE_EXHAUSTED"] = 8] = "RESOURCE_EXHAUSTED";
    StatusCodes2[StatusCodes2["FAILED_PRECONDITION"] = 9] = "FAILED_PRECONDITION";
    StatusCodes2[StatusCodes2["ABORTED"] = 10] = "ABORTED";
    StatusCodes2[StatusCodes2["OUT_OF_RANGE"] = 11] = "OUT_OF_RANGE";
    StatusCodes2[StatusCodes2["UNIMPLEMENTED"] = 12] = "UNIMPLEMENTED";
    StatusCodes2[StatusCodes2["INTERNAL"] = 13] = "INTERNAL";
    StatusCodes2[StatusCodes2["UNAVAILABLE"] = 14] = "UNAVAILABLE";
    StatusCodes2[StatusCodes2["DATA_LOSS"] = 15] = "DATA_LOSS";
    return StatusCodes2;
})(StatusCodes || {});
const StatusNameSchema = z.enum([
    "OK",
    "CANCELLED",
    "UNKNOWN",
    "INVALID_ARGUMENT",
    "DEADLINE_EXCEEDED",
    "NOT_FOUND",
    "ALREADY_EXISTS",
    "PERMISSION_DENIED",
    "UNAUTHENTICATED",
    "RESOURCE_EXHAUSTED",
    "FAILED_PRECONDITION",
    "ABORTED",
    "OUT_OF_RANGE",
    "UNIMPLEMENTED",
    "INTERNAL",
    "UNAVAILABLE",
    "DATA_LOSS"
]);
const statusCodeMap = {
    OK: 200,
    CANCELLED: 499,
    UNKNOWN: 500,
    INVALID_ARGUMENT: 400,
    DEADLINE_EXCEEDED: 504,
    NOT_FOUND: 404,
    ALREADY_EXISTS: 409,
    PERMISSION_DENIED: 403,
    UNAUTHENTICATED: 401,
    RESOURCE_EXHAUSTED: 429,
    FAILED_PRECONDITION: 400,
    ABORTED: 409,
    OUT_OF_RANGE: 400,
    UNIMPLEMENTED: 501,
    INTERNAL: 500,
    UNAVAILABLE: 503,
    DATA_LOSS: 500
};
function httpStatusCode(status) {
    if (!(status in statusCodeMap)) {
        throw new Error(`Invalid status code ${status}`);
    }
    return statusCodeMap[status];
}
const StatusCodesSchema = z.nativeEnum(StatusCodes);
const StatusSchema = z.object({
    code: StatusCodesSchema,
    message: z.string(),
    details: z.any().optional()
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    StatusCodes,
    StatusNameSchema,
    StatusSchema,
    httpStatusCode
}); //# sourceMappingURL=statusTypes.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var error_exports = {};
__export(error_exports, {
    GenkitError: ()=>GenkitError,
    UnstableApiError: ()=>UnstableApiError,
    UserFacingError: ()=>UserFacingError,
    assertUnstable: ()=>assertUnstable,
    getCallableJSON: ()=>getCallableJSON,
    getErrorMessage: ()=>getErrorMessage,
    getErrorStack: ()=>getErrorStack,
    getHttpStatus: ()=>getHttpStatus
});
module.exports = __toCommonJS(error_exports);
var import_statusTypes = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/statusTypes.js [app-rsc] (ecmascript)");
class GenkitError extends Error {
    source;
    status;
    detail;
    code;
    // For easy printing, we wrap the error with information like the source
    // and status, but that's redundant with JSON.
    originalMessage;
    constructor({ status, message, detail, source }){
        super(`${source ? `${source}: ` : ""}${status}: ${message}`);
        this.originalMessage = message;
        this.code = (0, import_statusTypes.httpStatusCode)(status);
        this.status = status;
        this.detail = detail;
        this.name = "GenkitError";
    }
    /**
   * Returns a JSON-serializable representation of this object.
   */ toJSON() {
        return {
            // This error type is used by 3P authors with the field "detail",
            // but the actual Callable protocol value is "details"
            ...this.detail === void 0 ? {} : {
                details: this.detail
            },
            status: this.status,
            message: this.originalMessage
        };
    }
}
class UnstableApiError extends GenkitError {
    constructor(level, message){
        super({
            status: "FAILED_PRECONDITION",
            message: `${message ? message + " " : ""}This API requires '${level}' stability level.

To use this feature, initialize Genkit using \`import {genkit} from "genkit/${level}"\`.`
        });
        this.name = "UnstableApiError";
    }
}
function assertUnstable(registry, level, message) {
    if (level === "beta" && registry.apiStability === "stable") {
        throw new UnstableApiError(level, message);
    }
}
class UserFacingError extends GenkitError {
    constructor(status, message, details){
        super({
            status,
            detail: details,
            message
        });
        super.name = "UserFacingError";
    }
}
function getHttpStatus(e) {
    if (e instanceof GenkitError) {
        return e.code;
    }
    return 500;
}
function getCallableJSON(e) {
    if (e instanceof GenkitError) {
        return e.toJSON();
    }
    return {
        message: "Internal Error",
        status: "INTERNAL"
    };
}
function getErrorMessage(e) {
    if (e instanceof Error) {
        return e.message;
    }
    return `${e}`;
}
function getErrorStack(e) {
    if (e instanceof Error) {
        return e.stack;
    }
    return void 0;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    GenkitError,
    UnstableApiError,
    UserFacingError,
    assertUnstable,
    getCallableJSON,
    getErrorMessage,
    getErrorStack,
    getHttpStatus
}); //# sourceMappingURL=error.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var async_context_exports = {};
__export(async_context_exports, {
    getAsyncContext: ()=>getAsyncContext,
    setAsyncContext: ()=>setAsyncContext
});
module.exports = __toCommonJS(async_context_exports);
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
const asyncContextKey = "__genkit_AsyncContext";
function getAsyncContext() {
    if (!global[asyncContextKey]) {
        throw new import_error.GenkitError({
            status: "FAILED_PRECONDITION",
            message: "Async context is not initialized."
        });
    }
    return global[asyncContextKey];
}
function setAsyncContext(context) {
    if (global[asyncContextKey]) return;
    global[asyncContextKey] = context;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    getAsyncContext,
    setAsyncContext
}); //# sourceMappingURL=async-context.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/async.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var async_exports = {};
__export(async_exports, {
    Channel: ()=>Channel,
    LazyPromise: ()=>LazyPromise,
    lazy: ()=>lazy
});
module.exports = __toCommonJS(async_exports);
function createTask() {
    let resolve, reject;
    const promise = new Promise((res, rej)=>([resolve, reject] = [
            res,
            rej
        ]));
    return {
        resolve,
        reject,
        promise
    };
}
class Channel {
    ready = createTask();
    buffer = [];
    err = null;
    send(value) {
        this.buffer.push(value);
        this.ready.resolve();
    }
    close() {
        this.buffer.push(null);
        this.ready.resolve();
    }
    error(err) {
        this.err = err;
        this.ready.reject(err);
    }
    [Symbol.asyncIterator]() {
        return {
            next: async ()=>{
                if (this.err) {
                    throw this.err;
                }
                if (!this.buffer.length) {
                    await this.ready.promise;
                }
                const value = this.buffer.shift();
                if (!this.buffer.length) {
                    this.ready = createTask();
                }
                return {
                    value,
                    done: !value
                };
            }
        };
    }
}
class LazyPromise {
    executor;
    promise;
    constructor(executor){
        this.executor = executor;
    }
    then(onfulfilled, onrejected) {
        this.promise ??= new Promise(this.executor);
        return this.promise.then(onfulfilled, onrejected);
    }
}
function lazy(fn) {
    return new LazyPromise((resolve, reject)=>{
        try {
            resolve(fn());
        } catch (e) {
            reject(e);
        }
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Channel,
    LazyPromise,
    lazy
}); //# sourceMappingURL=async.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var context_exports = {};
__export(context_exports, {
    apiKey: ()=>apiKey,
    getContext: ()=>getContext,
    runWithContext: ()=>runWithContext
});
module.exports = __toCommonJS(context_exports);
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var import_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
const contextAlsKey = "core.auth.context";
function runWithContext(context, fn) {
    if (context === void 0) {
        return fn();
    }
    return (0, import_async_context.getAsyncContext)().run(contextAlsKey, context, ()=>(0, import_action.runInActionRuntimeContext)(fn));
}
function getContext() {
    return (0, import_async_context.getAsyncContext)().getStore(contextAlsKey);
}
function apiKey(valueOrPolicy) {
    return async (request)=>{
        const context = {
            auth: {
                apiKey: request.headers["authorization"]
            }
        };
        if (typeof valueOrPolicy === "string") {
            if (!context.auth?.apiKey) {
                console.error("THROWING UNAUTHENTICATED");
                throw new import_error.UserFacingError("UNAUTHENTICATED", "Unauthenticated");
            }
            if (context.auth?.apiKey != valueOrPolicy) {
                console.error("Throwing PERMISSION_DENIED");
                throw new import_error.UserFacingError("PERMISSION_DENIED", "Permission Denied");
            }
        } else if (typeof valueOrPolicy === "function") {
            await valueOrPolicy(context);
        } else if (typeof valueOrPolicy !== "undefined") {
            throw new Error(`Invalid type ${typeof valueOrPolicy} passed to apiKey()`);
        }
        return context;
    };
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    apiKey,
    getContext,
    runWithContext
}); //# sourceMappingURL=context.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var schema_exports = {};
__export(schema_exports, {
    ValidationError: ()=>ValidationError,
    defineJsonSchema: ()=>defineJsonSchema,
    defineSchema: ()=>defineSchema,
    parseSchema: ()=>parseSchema,
    toJsonSchema: ()=>toJsonSchema,
    validateSchema: ()=>validateSchema,
    z: ()=>import_zod.z
});
module.exports = __toCommonJS(schema_exports);
var import_ajv = __toESM(__turbopack_context__.r("[project]/node_modules/ajv/dist/ajv.js [app-rsc] (ecmascript)"));
var import_ajv_formats = __toESM(__turbopack_context__.r("[project]/node_modules/ajv-formats/dist/index.js [app-rsc] (ecmascript)"));
var import_zod = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)");
var import_zod_to_json_schema = __toESM(__turbopack_context__.r("[project]/node_modules/zod-to-json-schema/dist/cjs/index.js [app-rsc] (ecmascript)"));
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
const ajv = new import_ajv.default();
(0, import_ajv_formats.default)(ajv);
const jsonSchemas = /* @__PURE__ */ new WeakMap();
const validators = /* @__PURE__ */ new WeakMap();
class ValidationError extends import_error.GenkitError {
    constructor({ data, errors, schema }){
        super({
            status: "INVALID_ARGUMENT",
            message: `Schema validation failed. Parse Errors:

${errors.map((e)=>`- ${e.path}: ${e.message}`).join("\n")}

Provided data:

${JSON.stringify(data, null, 2)}

Required JSON schema:

${JSON.stringify(schema, null, 2)}`,
            detail: {
                errors,
                schema
            }
        });
    }
}
function toJsonSchema({ jsonSchema, schema }) {
    if (!jsonSchema && !schema) return null;
    if (jsonSchema) return jsonSchema;
    if (jsonSchemas.has(schema)) return jsonSchemas.get(schema);
    const outSchema = (0, import_zod_to_json_schema.default)(schema, {
        $refStrategy: "none",
        removeAdditionalStrategy: "strict"
    });
    jsonSchemas.set(schema, outSchema);
    return outSchema;
}
function toErrorDetail(error) {
    return {
        path: error.instancePath.substring(1).replace(/\//g, ".") || "(root)",
        message: error.message
    };
}
function validateSchema(data, options) {
    const toValidate = toJsonSchema(options);
    if (!toValidate) {
        return {
            valid: true,
            schema: toValidate
        };
    }
    const validator = validators.get(toValidate) || ajv.compile(toValidate);
    const valid = validator(data);
    const errors = validator.errors?.map((e)=>e);
    return {
        valid,
        errors: errors?.map(toErrorDetail),
        schema: toValidate
    };
}
function parseSchema(data, options) {
    const { valid, errors, schema } = validateSchema(data, options);
    if (!valid) throw new ValidationError({
        data,
        errors,
        schema
    });
    return data;
}
function defineSchema(registry, name, schema) {
    registry.registerSchema(name, {
        schema
    });
    return schema;
}
function defineJsonSchema(registry, name, jsonSchema) {
    registry.registerSchema(name, {
        jsonSchema
    });
    return jsonSchema;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ValidationError,
    defineJsonSchema,
    defineSchema,
    parseSchema,
    toJsonSchema,
    validateSchema,
    z
}); //# sourceMappingURL=schema.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var logging_exports = {};
__export(logging_exports, {
    logger: ()=>logger
});
module.exports = __toCommonJS(logging_exports);
const LOG_LEVELS = [
    "debug",
    "info",
    "warn",
    "error"
];
const loggerKey = "__genkit_logger";
const _defaultLogger = {
    shouldLog (targetLevel) {
        return LOG_LEVELS.indexOf(this.level) <= LOG_LEVELS.indexOf(targetLevel);
    },
    debug (...args) {
        this.shouldLog("debug") && console.debug(...args);
    },
    info (...args) {
        this.shouldLog("info") && console.info(...args);
    },
    warn (...args) {
        this.shouldLog("warn") && console.warn(...args);
    },
    error (...args) {
        this.shouldLog("error") && console.error(...args);
    },
    level: "info"
};
function getLogger() {
    if (!global[loggerKey]) {
        global[loggerKey] = _defaultLogger;
    }
    return global[loggerKey];
}
class Logger {
    defaultLogger = _defaultLogger;
    init(fn) {
        global[loggerKey] = fn;
    }
    info(...args) {
        getLogger().info.apply(getLogger(), args);
    }
    debug(...args) {
        getLogger().debug.apply(getLogger(), args);
    }
    error(...args) {
        getLogger().error.apply(getLogger(), args);
    }
    warn(...args) {
        getLogger().warn.apply(getLogger(), args);
    }
    setLogLevel(level) {
        getLogger().level = level;
    }
    logStructured(msg, metadata) {
        getLogger().info(msg, metadata);
    }
    logStructuredError(msg, metadata) {
        getLogger().error(msg, metadata);
    }
}
const logger = new Logger();
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    logger
}); //# sourceMappingURL=logging.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var utils_exports = {};
__export(utils_exports, {
    deleteUndefinedProps: ()=>deleteUndefinedProps,
    featureMetadataPrefix: ()=>featureMetadataPrefix,
    getCurrentEnv: ()=>getCurrentEnv,
    isDevEnv: ()=>isDevEnv,
    stripUndefinedProps: ()=>stripUndefinedProps
});
module.exports = __toCommonJS(utils_exports);
function deleteUndefinedProps(obj) {
    for(const prop in obj){
        if (obj[prop] === void 0) {
            delete obj[prop];
        } else {
            if (typeof obj[prop] === "object") {
                deleteUndefinedProps(obj[prop]);
            }
        }
    }
}
function stripUndefinedProps(input) {
    if (input === void 0 || input === null || Array.isArray(input) || typeof input !== "object") {
        return input;
    }
    const out = {};
    for(const key in input){
        if (input[key] !== void 0) {
            out[key] = stripUndefinedProps(input[key]);
        }
    }
    return out;
}
function getCurrentEnv() {
    return process.env.GENKIT_ENV || "prod";
}
function isDevEnv() {
    return getCurrentEnv() === "dev";
}
function featureMetadataPrefix(name) {
    return `feature:${name}`;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    deleteUndefinedProps,
    featureMetadataPrefix,
    getCurrentEnv,
    isDevEnv,
    stripUndefinedProps
}); //# sourceMappingURL=utils.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var exporter_exports = {};
__export(exporter_exports, {
    TraceServerExporter: ()=>TraceServerExporter,
    setTelemetryServerUrl: ()=>setTelemetryServerUrl,
    telemetryServerUrl: ()=>telemetryServerUrl
});
module.exports = __toCommonJS(exporter_exports);
var import_api = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var import_core = __turbopack_context__.r("[project]/node_modules/@opentelemetry/core/build/esm/index.js [app-rsc] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var import_utils = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)");
let telemetryServerUrl;
function setTelemetryServerUrl(url) {
    telemetryServerUrl = url;
}
class TraceServerExporter {
    /**
   * Export spans.
   * @param spans
   * @param resultCallback
   */ export(spans, resultCallback) {
        this._sendSpans(spans, resultCallback);
    }
    /**
   * Shutdown the exporter.
   */ shutdown() {
        this._sendSpans([]);
        return this.forceFlush();
    }
    /**
   * Converts span info into trace store format.
   * @param span
   */ _exportInfo(span) {
        const spanData = {
            spanId: span.spanContext().spanId,
            traceId: span.spanContext().traceId,
            startTime: transformTime(span.startTime),
            endTime: transformTime(span.endTime),
            attributes: {
                ...span.attributes
            },
            displayName: span.name,
            links: span.links,
            spanKind: import_api.SpanKind[span.kind],
            parentSpanId: span.parentSpanId,
            sameProcessAsParentSpan: {
                value: !span.spanContext().isRemote
            },
            status: span.status,
            timeEvents: {
                timeEvent: span.events.map((e)=>({
                        time: transformTime(e.time),
                        annotation: {
                            attributes: e.attributes ?? {},
                            description: e.name
                        }
                    }))
            }
        };
        if (span.instrumentationLibrary !== void 0) {
            spanData.instrumentationLibrary = {
                name: span.instrumentationLibrary.name
            };
            if (span.instrumentationLibrary.schemaUrl !== void 0) {
                spanData.instrumentationLibrary.schemaUrl = span.instrumentationLibrary.schemaUrl;
            }
            if (span.instrumentationLibrary.version !== void 0) {
                spanData.instrumentationLibrary.version = span.instrumentationLibrary.version;
            }
        }
        (0, import_utils.deleteUndefinedProps)(spanData);
        return spanData;
    }
    /**
   * Exports any pending spans in exporter
   */ forceFlush() {
        return Promise.resolve();
    }
    async _sendSpans(spans, done) {
        const traces = {};
        for (const span of spans){
            if (!traces[span.spanContext().traceId]) {
                traces[span.spanContext().traceId] = [];
            }
            traces[span.spanContext().traceId].push(span);
        }
        let error = false;
        for (const traceId of Object.keys(traces)){
            try {
                await this.save(traceId, traces[traceId]);
            } catch (e) {
                error = true;
                import_logging.logger.error(`Failed to save trace ${traceId}`, e);
            }
            if (done) {
                return done({
                    code: error ? import_core.ExportResultCode.FAILED : import_core.ExportResultCode.SUCCESS
                });
            }
        }
    }
    async save(traceId, spans) {
        if (!telemetryServerUrl) {
            import_logging.logger.debug(`Telemetry server is not configured, trace ${traceId} not saved!`);
            return;
        }
        const data = {
            traceId,
            spans: {}
        };
        for (const span of spans){
            const convertedSpan = this._exportInfo(span);
            data.spans[convertedSpan.spanId] = convertedSpan;
            if (!convertedSpan.parentSpanId) {
                data.displayName = convertedSpan.displayName;
                data.startTime = convertedSpan.startTime;
                data.endTime = convertedSpan.endTime;
            }
        }
        await fetch(`${telemetryServerUrl}/api/traces`, {
            method: "POST",
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        });
    }
}
function transformTime(time) {
    return (0, import_core.hrTimeToMilliseconds)(time);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    TraceServerExporter,
    setTelemetryServerUrl,
    telemetryServerUrl
}); //# sourceMappingURL=exporter.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var instrumentation_exports = {};
__export(instrumentation_exports, {
    ATTR_PREFIX: ()=>ATTR_PREFIX,
    SPAN_TYPE_ATTR: ()=>SPAN_TYPE_ATTR,
    appendSpan: ()=>appendSpan,
    runInNewSpan: ()=>runInNewSpan,
    setCustomMetadataAttribute: ()=>setCustomMetadataAttribute,
    setCustomMetadataAttributes: ()=>setCustomMetadataAttributes,
    spanMetadataAlsKey: ()=>spanMetadataAlsKey,
    toDisplayPath: ()=>toDisplayPath
});
module.exports = __toCommonJS(instrumentation_exports);
var import_api = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
var import_node_perf_hooks = __turbopack_context__.r("[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)");
var import_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)");
const spanMetadataAlsKey = "core.tracing.instrumentation.span";
const ATTR_PREFIX = "genkit";
const SPAN_TYPE_ATTR = ATTR_PREFIX + ":type";
const TRACER_NAME = "genkit-tracer";
const TRACER_VERSION = "v1";
async function runInNewSpan(registryOrOprs, optsOrFn, fnMaybe) {
    let opts;
    let fn;
    if (arguments.length === 3) {
        opts = optsOrFn;
        fn = fnMaybe;
    } else {
        opts = registryOrOprs;
        fn = optsOrFn;
    }
    await (0, import_tracing.ensureBasicTelemetryInstrumentation)();
    const tracer = import_api.trace.getTracer(TRACER_NAME, TRACER_VERSION);
    const parentStep = (0, import_async_context.getAsyncContext)().getStore(spanMetadataAlsKey);
    const isInRoot = parentStep?.metadata?.isRoot === true;
    if (!parentStep) opts.metadata.isRoot ||= true;
    return await tracer.startActiveSpan(opts.metadata.name, {
        links: opts.links,
        root: opts.metadata.isRoot
    }, async (otSpan)=>{
        if (opts.labels) otSpan.setAttributes(opts.labels);
        const spanContext = {
            ...parentStep,
            metadata: opts.metadata
        };
        try {
            opts.metadata.path = buildPath(opts.metadata.name, parentStep?.metadata?.path || "", opts.labels);
            const output = await (0, import_async_context.getAsyncContext)().run(spanMetadataAlsKey, spanContext, ()=>fn(opts.metadata, otSpan, isInRoot));
            if (opts.metadata.state !== "error") {
                opts.metadata.state = "success";
            }
            recordPath(opts.metadata, spanContext);
            return output;
        } catch (e) {
            recordPath(opts.metadata, spanContext, e);
            opts.metadata.state = "error";
            otSpan.setStatus({
                code: import_api.SpanStatusCode.ERROR,
                message: getErrorMessage(e)
            });
            if (e instanceof Error) {
                otSpan.recordException(e);
            }
            if (typeof e === "object") {
                if (!e.ignoreFailedSpan) {
                    opts.metadata.isFailureSource = true;
                }
                e.ignoreFailedSpan = true;
            }
            throw e;
        } finally{
            otSpan.setAttributes(metadataToAttributes(opts.metadata));
            otSpan.end();
        }
    });
}
async function appendSpan(traceId, parentSpanId, metadata, labels) {
    await (0, import_tracing.ensureBasicTelemetryInstrumentation)();
    const tracer = import_api.trace.getTracer(TRACER_NAME, TRACER_VERSION);
    const spanContext = import_api.trace.setSpanContext(import_api.ROOT_CONTEXT, {
        traceId,
        traceFlags: 1,
        // sampled
        spanId: parentSpanId
    });
    const span = tracer.startSpan(metadata.name, {}, spanContext);
    span.setAttributes(metadataToAttributes(metadata));
    if (labels) {
        span.setAttributes(labels);
    }
    span.end();
}
function getErrorMessage(e) {
    if (e instanceof Error) {
        return e.message;
    }
    return `${e}`;
}
function metadataToAttributes(metadata) {
    const out = {};
    Object.keys(metadata).forEach((key)=>{
        if (key === "metadata" && typeof metadata[key] === "object" && metadata.metadata) {
            Object.entries(metadata.metadata).forEach(([metaKey, value])=>{
                out[ATTR_PREFIX + ":metadata:" + metaKey] = value;
            });
        } else if (key === "input" || typeof metadata[key] === "object") {
            out[ATTR_PREFIX + ":" + key] = JSON.stringify(metadata[key]);
        } else {
            out[ATTR_PREFIX + ":" + key] = metadata[key];
        }
    });
    return out;
}
function setCustomMetadataAttribute(key, value) {
    const currentStep = getCurrentSpan();
    if (!currentStep) {
        return;
    }
    if (!currentStep.metadata) {
        currentStep.metadata = {};
    }
    currentStep.metadata[key] = value;
}
function setCustomMetadataAttributes(values) {
    const currentStep = getCurrentSpan();
    if (!currentStep) {
        return;
    }
    if (!currentStep.metadata) {
        currentStep.metadata = {};
    }
    for (const [key, value] of Object.entries(values)){
        currentStep.metadata[key] = value;
    }
}
function toDisplayPath(path) {
    const pathPartRegex = /\{([^\,}]+),[^\}]+\}/g;
    return Array.from(path.matchAll(pathPartRegex), (m)=>m[1]).join(" > ");
}
function getCurrentSpan() {
    const step = (0, import_async_context.getAsyncContext)().getStore(spanMetadataAlsKey);
    if (!step) {
        throw new Error("running outside step context");
    }
    return step.metadata;
}
function buildPath(name, parentPath, labels) {
    const stepType = labels && labels["genkit:type"] ? `,t:${labels["genkit:metadata:subtype"] === "flow" ? "flow" : labels["genkit:type"]}` : "";
    return parentPath + `/{${name}${stepType}}`;
}
function recordPath(spanMeta, spanContext, err) {
    const path = spanMeta.path || "";
    const decoratedPath = decoratePathWithSubtype(spanMeta);
    const paths = Array.from(spanContext?.paths || /* @__PURE__ */ new Set());
    const status = err ? "failure" : "success";
    if (!paths.some((p)=>p.path.startsWith(path) && p.status === status)) {
        const now = import_node_perf_hooks.performance.now();
        const start = spanContext?.timestamp || now;
        spanContext?.paths?.add({
            path: decoratedPath,
            error: err?.name,
            latency: now - start,
            status
        });
    }
    spanMeta.path = decoratedPath;
}
function decoratePathWithSubtype(metadata) {
    if (!metadata.path) {
        return "";
    }
    const pathComponents = metadata.path.split("}/{");
    if (pathComponents.length == 1) {
        return metadata.path;
    }
    const stepSubtype = metadata.metadata && metadata.metadata["subtype"] ? `,s:${metadata.metadata["subtype"]}` : "";
    const root = `${pathComponents.slice(0, -1).join("}/{")}}/`;
    const decoratedStep = `{${pathComponents.at(-1)?.slice(0, -1)}${stepSubtype}}`;
    return root + decoratedStep;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ATTR_PREFIX,
    SPAN_TYPE_ATTR,
    appendSpan,
    runInNewSpan,
    setCustomMetadataAttribute,
    setCustomMetadataAttributes,
    spanMetadataAlsKey,
    toDisplayPath
}); //# sourceMappingURL=instrumentation.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var types_exports = {};
__export(types_exports, {
    InstrumentationLibrarySchema: ()=>InstrumentationLibrarySchema,
    LinkSchema: ()=>LinkSchema,
    PathMetadataSchema: ()=>PathMetadataSchema,
    SpanContextSchema: ()=>SpanContextSchema,
    SpanDataSchema: ()=>SpanDataSchema,
    SpanMetadataSchema: ()=>SpanMetadataSchema,
    SpanStatusSchema: ()=>SpanStatusSchema,
    TimeEventSchema: ()=>TimeEventSchema,
    TraceDataSchema: ()=>TraceDataSchema,
    TraceMetadataSchema: ()=>TraceMetadataSchema
});
module.exports = __toCommonJS(types_exports);
var import_zod = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)");
const PathMetadataSchema = import_zod.z.object({
    path: import_zod.z.string(),
    status: import_zod.z.string(),
    error: import_zod.z.string().optional(),
    latency: import_zod.z.number()
});
const TraceMetadataSchema = import_zod.z.object({
    featureName: import_zod.z.string().optional(),
    paths: import_zod.z.set(PathMetadataSchema).optional(),
    timestamp: import_zod.z.number()
});
const SpanMetadataSchema = import_zod.z.object({
    name: import_zod.z.string(),
    state: import_zod.z.enum([
        "success",
        "error"
    ]).optional(),
    input: import_zod.z.any().optional(),
    output: import_zod.z.any().optional(),
    isRoot: import_zod.z.boolean().optional(),
    metadata: import_zod.z.record(import_zod.z.string(), import_zod.z.string()).optional(),
    path: import_zod.z.string().optional(),
    // Indicates a "leaf" span that is the source of a failure.
    isFailureSource: import_zod.z.boolean().optional()
});
const SpanStatusSchema = import_zod.z.object({
    code: import_zod.z.number(),
    message: import_zod.z.string().optional()
});
const TimeEventSchema = import_zod.z.object({
    time: import_zod.z.number(),
    annotation: import_zod.z.object({
        attributes: import_zod.z.record(import_zod.z.string(), import_zod.z.any()),
        description: import_zod.z.string()
    })
});
const SpanContextSchema = import_zod.z.object({
    traceId: import_zod.z.string(),
    spanId: import_zod.z.string(),
    isRemote: import_zod.z.boolean().optional(),
    traceFlags: import_zod.z.number()
});
const LinkSchema = import_zod.z.object({
    context: SpanContextSchema.optional(),
    attributes: import_zod.z.record(import_zod.z.string(), import_zod.z.any()).optional(),
    droppedAttributesCount: import_zod.z.number().optional()
});
const InstrumentationLibrarySchema = import_zod.z.object({
    name: import_zod.z.string().readonly(),
    version: import_zod.z.string().optional().readonly(),
    schemaUrl: import_zod.z.string().optional().readonly()
});
const SpanDataSchema = import_zod.z.object({
    spanId: import_zod.z.string(),
    traceId: import_zod.z.string(),
    parentSpanId: import_zod.z.string().optional(),
    startTime: import_zod.z.number(),
    endTime: import_zod.z.number(),
    attributes: import_zod.z.record(import_zod.z.string(), import_zod.z.any()),
    displayName: import_zod.z.string(),
    links: import_zod.z.array(LinkSchema).optional(),
    instrumentationLibrary: InstrumentationLibrarySchema,
    spanKind: import_zod.z.string(),
    sameProcessAsParentSpan: import_zod.z.object({
        value: import_zod.z.boolean()
    }).optional(),
    status: SpanStatusSchema.optional(),
    timeEvents: import_zod.z.object({
        timeEvent: import_zod.z.array(TimeEventSchema)
    }).optional(),
    truncated: import_zod.z.boolean().optional()
});
const TraceDataSchema = import_zod.z.object({
    traceId: import_zod.z.string(),
    displayName: import_zod.z.string().optional(),
    startTime: import_zod.z.number().optional().describe("trace start time in milliseconds since the epoch"),
    endTime: import_zod.z.number().optional().describe("end time in milliseconds since the epoch"),
    spans: import_zod.z.record(import_zod.z.string(), SpanDataSchema)
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    InstrumentationLibrarySchema,
    LinkSchema,
    PathMetadataSchema,
    SpanContextSchema,
    SpanDataSchema,
    SpanMetadataSchema,
    SpanStatusSchema,
    TimeEventSchema,
    TraceDataSchema,
    TraceMetadataSchema
}); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var tracing_exports = {};
__export(tracing_exports, {
    enableTelemetry: ()=>enableTelemetry,
    ensureBasicTelemetryInstrumentation: ()=>ensureBasicTelemetryInstrumentation,
    flushTracing: ()=>flushTracing,
    setTelemetryProvider: ()=>setTelemetryProvider
});
module.exports = __toCommonJS(tracing_exports);
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
__reExport(tracing_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)"), module.exports);
__reExport(tracing_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)"), module.exports);
__reExport(tracing_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)"), module.exports);
const instrumentationKey = "__GENKIT_TELEMETRY_INSTRUMENTED";
const telemetryProviderKey = "__GENKIT_TELEMETRY_PROVIDER";
async function ensureBasicTelemetryInstrumentation() {
    await checkFirebaseMonitoringAutoInit();
    if (global[instrumentationKey]) {
        return await global[instrumentationKey];
    }
    await enableTelemetry({});
}
async function checkFirebaseMonitoringAutoInit() {
    if (!global[instrumentationKey] && process.env.ENABLE_FIREBASE_MONITORING === "true") {
        try {
            const importModule = new Function("moduleName", "return import(moduleName)");
            const firebaseModule = await importModule("@genkit-ai/firebase");
            firebaseModule.enableFirebaseTelemetry();
        } catch (e) {
            import_logging.logger.warn("It looks like you're trying to enable firebase monitoring, but haven't installed the firebase plugin. Please run `npm i --save @genkit-ai/firebase` and redeploy.");
        }
    }
}
function getTelemetryProvider() {
    if (global[telemetryProviderKey]) {
        return global[telemetryProviderKey];
    }
    throw new import_error.GenkitError({
        status: "FAILED_PRECONDITION",
        message: "TelemetryProvider is not initialized."
    });
}
function setTelemetryProvider(provider) {
    if (global[telemetryProviderKey]) return;
    global[telemetryProviderKey] = provider;
}
async function enableTelemetry(telemetryConfig) {
    global[instrumentationKey] = telemetryConfig instanceof Promise ? telemetryConfig : Promise.resolve();
    return getTelemetryProvider().enableTelemetry(telemetryConfig);
}
async function flushTracing() {
    return getTelemetryProvider().flushTracing();
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    enableTelemetry,
    ensureBasicTelemetryInstrumentation,
    flushTracing,
    setTelemetryProvider,
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)")
}); //# sourceMappingURL=tracing.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var action_exports = {};
__export(action_exports, {
    StatusCodes: ()=>import_statusTypes.StatusCodes,
    StatusSchema: ()=>import_statusTypes.StatusSchema,
    action: ()=>action,
    actionWithMiddleware: ()=>actionWithMiddleware,
    defineAction: ()=>defineAction,
    defineActionAsync: ()=>defineActionAsync,
    getStreamingCallback: ()=>getStreamingCallback,
    isAction: ()=>isAction,
    isInRuntimeContext: ()=>isInRuntimeContext,
    runInActionRuntimeContext: ()=>runInActionRuntimeContext,
    runOutsideActionRuntimeContext: ()=>runOutsideActionRuntimeContext,
    runWithStreamingCallback: ()=>runWithStreamingCallback,
    sentinelNoopStreamingCallback: ()=>sentinelNoopStreamingCallback
});
module.exports = __toCommonJS(action_exports);
var import_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var import_async = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async.js [app-rsc] (ecmascript)");
var import_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)");
var import_statusTypes = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/statusTypes.js [app-rsc] (ecmascript)");
const makeNoopAbortSignal = ()=>new AbortController().signal;
function actionWithMiddleware(action2, middleware) {
    const wrapped = async (req, options)=>{
        return (await wrapped.run(req, options)).result;
    };
    wrapped.__action = action2.__action;
    wrapped.run = async (req, options)=>{
        let telemetry;
        const dispatch = async (index, req2, opts)=>{
            if (index === middleware.length) {
                const result = await action2.run(req2, opts);
                telemetry = result.telemetry;
                return result.result;
            }
            const currentMiddleware = middleware[index];
            if (currentMiddleware.length === 3) {
                return currentMiddleware(req2, opts, async (modifiedReq, modifiedOptions)=>dispatch(index + 1, modifiedReq || req2, modifiedOptions || opts));
            } else if (currentMiddleware.length === 2) {
                return currentMiddleware(req2, async (modifiedReq)=>dispatch(index + 1, modifiedReq || req2, opts));
            } else {
                throw new Error("unspported middleware function shape");
            }
        };
        wrapped.stream = action2.stream;
        return {
            result: await dispatch(0, req, options),
            telemetry
        };
    };
    return wrapped;
}
function action(config, fn) {
    const actionName = typeof config.name === "string" ? config.name : `${config.name.pluginId}/${config.name.actionId}`;
    const actionMetadata = {
        name: actionName,
        description: config.description,
        inputSchema: config.inputSchema,
        inputJsonSchema: config.inputJsonSchema,
        outputSchema: config.outputSchema,
        outputJsonSchema: config.outputJsonSchema,
        streamSchema: config.streamSchema,
        metadata: config.metadata,
        actionType: config.actionType
    };
    const actionFn = async (input, options)=>{
        return (await actionFn.run(input, options)).result;
    };
    actionFn.__action = {
        ...actionMetadata
    };
    actionFn.run = async (input, options)=>{
        input = (0, import_schema.parseSchema)(input, {
            schema: config.inputSchema,
            jsonSchema: config.inputJsonSchema
        });
        let traceId;
        let spanId;
        let output = await (0, import_tracing.runInNewSpan)({
            metadata: {
                name: actionName
            },
            labels: {
                [import_tracing.SPAN_TYPE_ATTR]: "action",
                "genkit:metadata:subtype": config.actionType,
                ...options?.telemetryLabels
            }
        }, async (metadata, span)=>{
            (0, import_tracing.setCustomMetadataAttributes)({
                subtype: config.actionType
            });
            if (options?.context) {
                (0, import_tracing.setCustomMetadataAttributes)({
                    context: JSON.stringify(options.context)
                });
            }
            traceId = span.spanContext().traceId;
            spanId = span.spanContext().spanId;
            metadata.name = actionName;
            metadata.input = input;
            try {
                const actFn = ()=>fn(input, {
                        ...options,
                        // Context can either be explicitly set, or inherited from the parent action.
                        context: {
                            ...actionFn.__registry?.context,
                            ...options?.context ?? (0, import_context.getContext)()
                        },
                        streamingRequested: !!options?.onChunk && options.onChunk !== sentinelNoopStreamingCallback,
                        sendChunk: options?.onChunk ?? sentinelNoopStreamingCallback,
                        trace: {
                            traceId,
                            spanId
                        },
                        registry: actionFn.__registry,
                        abortSignal: options?.abortSignal ?? makeNoopAbortSignal()
                    });
                const output2 = await (0, import_context.runWithContext)(options?.context, actFn);
                metadata.output = JSON.stringify(output2);
                return output2;
            } catch (err) {
                if (typeof err === "object") {
                    err.traceId = traceId;
                }
                throw err;
            }
        });
        output = (0, import_schema.parseSchema)(output, {
            schema: config.outputSchema,
            jsonSchema: config.outputJsonSchema
        });
        return {
            result: output,
            telemetry: {
                traceId,
                spanId
            }
        };
    };
    actionFn.stream = (input, opts)=>{
        let chunkStreamController;
        const chunkStream = new ReadableStream({
            start (controller) {
                chunkStreamController = controller;
            },
            pull () {},
            cancel () {}
        });
        const invocationPromise = actionFn.run(config.inputSchema ? config.inputSchema.parse(input) : input, {
            onChunk: (chunk)=>{
                chunkStreamController.enqueue(chunk);
            },
            context: {
                ...actionFn.__registry?.context,
                ...opts?.context ?? (0, import_context.getContext)()
            },
            abortSignal: opts?.abortSignal,
            telemetryLabels: opts?.telemetryLabels
        }).then((s)=>s.result).finally(()=>{
            chunkStreamController.close();
        });
        return {
            output: invocationPromise,
            stream: async function*() {
                const reader = chunkStream.getReader();
                while(true){
                    const chunk = await reader.read();
                    if (chunk.value) {
                        yield chunk.value;
                    }
                    if (chunk.done) {
                        break;
                    }
                }
                return await invocationPromise;
            }()
        };
    };
    if (config.use) {
        return actionWithMiddleware(actionFn, config.use);
    }
    return actionFn;
}
function isAction(a) {
    return typeof a === "function" && "__action" in a;
}
function defineAction(registry, config, fn) {
    if (isInRuntimeContext()) {
        throw new Error("Cannot define new actions at runtime.\nSee: https://github.com/firebase/genkit/blob/main/docs/errors/no_new_actions_at_runtime.md");
    }
    const act = action(config, async (i, options)=>{
        await registry.initializeAllPlugins();
        return await runInActionRuntimeContext(()=>fn(i, options));
    });
    act.__action.actionType = config.actionType;
    registry.registerAction(config.actionType, act);
    return act;
}
function defineActionAsync(registry, actionType, name, config, onInit) {
    const actionName = typeof name === "string" ? name : `${name.pluginId}/${name.actionId}`;
    const actionPromise = (0, import_async.lazy)(()=>config.then((resolvedConfig)=>{
            const act = action(resolvedConfig, async (i, options)=>{
                await registry.initializeAllPlugins();
                return await runInActionRuntimeContext(()=>resolvedConfig.fn(i, options));
            });
            act.__action.actionType = actionType;
            onInit?.(act);
            return act;
        }));
    registry.registerActionAsync(actionType, actionName, actionPromise);
    return actionPromise;
}
const streamingAlsKey = "core.action.streamingCallback";
const sentinelNoopStreamingCallback = ()=>null;
function runWithStreamingCallback(streamingCallback, fn) {
    return (0, import_async_context.getAsyncContext)().run(streamingAlsKey, streamingCallback || sentinelNoopStreamingCallback, fn);
}
function getStreamingCallback() {
    const cb = (0, import_async_context.getAsyncContext)().getStore(streamingAlsKey);
    if (cb === sentinelNoopStreamingCallback) {
        return void 0;
    }
    return cb;
}
const runtimeContextAslKey = "core.action.runtimeContext";
function isInRuntimeContext() {
    return (0, import_async_context.getAsyncContext)().getStore(runtimeContextAslKey) === "runtime";
}
function runInActionRuntimeContext(fn) {
    return (0, import_async_context.getAsyncContext)().run(runtimeContextAslKey, "runtime", fn);
}
function runOutsideActionRuntimeContext(fn) {
    return (0, import_async_context.getAsyncContext)().run(runtimeContextAslKey, "outside", fn);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    StatusCodes,
    StatusSchema,
    action,
    actionWithMiddleware,
    defineAction,
    defineActionAsync,
    getStreamingCallback,
    isAction,
    isInRuntimeContext,
    runInActionRuntimeContext,
    runOutsideActionRuntimeContext,
    runWithStreamingCallback,
    sentinelNoopStreamingCallback
}); //# sourceMappingURL=action.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var plugin_exports = {};
module.exports = __toCommonJS(plugin_exports); //# sourceMappingURL=plugin.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var reflection_exports = {};
__export(reflection_exports, {
    ReflectionServer: ()=>ReflectionServer,
    RunActionResponseSchema: ()=>RunActionResponseSchema
});
module.exports = __toCommonJS(reflection_exports);
var import_express = __toESM(__turbopack_context__.r("[externals]/express [external] (express, cjs)"));
var import_promises = __toESM(__turbopack_context__.r("[externals]/fs/promises [external] (fs/promises, cjs)"));
var import_get_port = __toESM(__turbopack_context__.r("[project]/node_modules/get-port/index.js [app-rsc] (ecmascript)"));
var import_path = __toESM(__turbopack_context__.r("[externals]/path [external] (path, cjs)"));
var z = __toESM(__turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)"));
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var import_index = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/index.js [app-rsc] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)");
const RunActionResponseSchema = z.object({
    result: z.unknown().optional(),
    error: z.unknown().optional(),
    telemetry: z.object({
        traceId: z.string().optional()
    }).optional()
});
class ReflectionServer {
    /** List of all running servers needed to be cleaned up on process exit. */ static RUNNING_SERVERS = [];
    /** Registry instance to be used for API calls. */ registry;
    /** Options for the reflection server. */ options;
    /** Port the server is actually running on. This may differ from `options.port` if the original was occupied. Null if server is not running. */ port = null;
    /** Express server instance. Null if server is not running. */ server = null;
    /** Path to the runtime file. Null if server is not running. */ runtimeFilePath = null;
    constructor(registry, options){
        this.registry = registry;
        this.options = {
            port: 3100,
            bodyLimit: "30mb",
            configuredEnvs: [
                "dev"
            ],
            ...options
        };
    }
    /**
   * Finds a free port to run the server on based on the original chosen port and environment.
   */ async findPort() {
        const chosenPort = this.options.port;
        const freePort = await (0, import_get_port.default)({
            port: (0, import_get_port.makeRange)(chosenPort, chosenPort + 100)
        });
        if (freePort !== chosenPort) {
            import_logging.logger.warn(`Port ${chosenPort} is already in use, using next available port ${freePort} instead.`);
        }
        return freePort;
    }
    /**
   * Starts the server.
   *
   * The server will be registered to be shut down on process exit.
   */ async start() {
        const server = (0, import_express.default)();
        server.use(import_express.default.json({
            limit: this.options.bodyLimit
        }));
        server.use((req, res, next)=>{
            res.header("x-genkit-version", import_index.GENKIT_VERSION);
            next();
        });
        server.get("/api/__health", async (_, response)=>{
            await this.registry.listActions();
            response.status(200).send("OK");
        });
        server.get("/api/__quitquitquit", async (_, response)=>{
            import_logging.logger.debug("Received quitquitquit");
            response.status(200).send("OK");
            await this.stop();
        });
        server.get("/api/actions", async (_, response, next)=>{
            import_logging.logger.debug("Fetching actions.");
            try {
                const actions = await this.registry.listResolvableActions();
                const convertedActions = {};
                Object.keys(actions).forEach((key)=>{
                    const action = actions[key];
                    convertedActions[key] = {
                        key,
                        name: action.name,
                        description: action.description,
                        metadata: action.metadata
                    };
                    if (action.inputSchema || action.inputJsonSchema) {
                        convertedActions[key].inputSchema = (0, import_schema.toJsonSchema)({
                            schema: action.inputSchema,
                            jsonSchema: action.inputJsonSchema
                        });
                    }
                    if (action.outputSchema || action.outputJsonSchema) {
                        convertedActions[key].outputSchema = (0, import_schema.toJsonSchema)({
                            schema: action.outputSchema,
                            jsonSchema: action.outputJsonSchema
                        });
                    }
                });
                response.send(convertedActions);
            } catch (err) {
                const { message, stack } = err;
                next({
                    message,
                    stack
                });
            }
        });
        server.post("/api/runAction", async (request, response, next)=>{
            const { key, input, context, telemetryLabels } = request.body;
            const { stream } = request.query;
            import_logging.logger.debug(`Running action \`${key}\` with stream=${stream}...`);
            try {
                const action = await this.registry.lookupAction(key);
                if (!action) {
                    response.status(404).send(`action ${key} not found`);
                    return;
                }
                if (stream === "true") {
                    try {
                        const callback = (chunk)=>{
                            response.write(JSON.stringify(chunk) + "\n");
                        };
                        const result = await action.run(input, {
                            context,
                            onChunk: callback,
                            telemetryLabels
                        });
                        await (0, import_tracing.flushTracing)();
                        response.write(JSON.stringify({
                            result: result.result,
                            telemetry: {
                                traceId: result.telemetry.traceId
                            }
                        }));
                        response.end();
                    } catch (err) {
                        const { message, stack } = err;
                        const errorResponse = {
                            code: import_action.StatusCodes.INTERNAL,
                            message,
                            details: {
                                stack
                            }
                        };
                        if (err.traceId) {
                            errorResponse.details.traceId = err.traceId;
                        }
                        response.write(JSON.stringify({
                            error: errorResponse
                        }));
                        response.end();
                    }
                } else {
                    const result = await action.run(input, {
                        context,
                        telemetryLabels
                    });
                    await (0, import_tracing.flushTracing)();
                    response.send({
                        result: result.result,
                        telemetry: {
                            traceId: result.telemetry.traceId
                        }
                    });
                }
            } catch (err) {
                const { message, stack, traceId } = err;
                next({
                    message,
                    stack,
                    traceId
                });
            }
        });
        server.get("/api/envs", async (_, response)=>{
            response.json(this.options.configuredEnvs);
        });
        server.post("/api/notify", async (request, response)=>{
            const { telemetryServerUrl, reflectionApiSpecVersion } = request.body;
            if (!process.env.GENKIT_TELEMETRY_SERVER) {
                if (typeof telemetryServerUrl === "string") {
                    (0, import_tracing.setTelemetryServerUrl)(telemetryServerUrl);
                    import_logging.logger.debug(`Connected to telemetry server on ${telemetryServerUrl}`);
                }
            }
            if (reflectionApiSpecVersion !== import_index.GENKIT_REFLECTION_API_SPEC_VERSION) {
                if (!reflectionApiSpecVersion || reflectionApiSpecVersion < import_index.GENKIT_REFLECTION_API_SPEC_VERSION) {
                    import_logging.logger.warn("WARNING: Genkit CLI version may be outdated. Please update `genkit-cli` to the latest version.");
                } else {
                    import_logging.logger.warn(`Genkit CLI is newer than runtime library. Some feature may not be supported. Consider upgrading your runtime library version (debug info: expected ${import_index.GENKIT_REFLECTION_API_SPEC_VERSION}, got ${reflectionApiSpecVersion}).`);
                }
            }
            response.status(200).send("OK");
        });
        server.use((err, req, res, next)=>{
            import_logging.logger.error(err.stack);
            const error = err;
            const { message, stack } = error;
            const errorResponse = {
                code: import_action.StatusCodes.INTERNAL,
                message,
                details: {
                    stack
                }
            };
            if (err.traceId) {
                errorResponse.details.traceId = err.traceId;
            }
            res.status(500).json(errorResponse);
        });
        this.port = await this.findPort();
        this.server = server.listen(this.port, async ()=>{
            import_logging.logger.debug(`Reflection server (${process.pid}) running on http://localhost:${this.port}`);
            ReflectionServer.RUNNING_SERVERS.push(this);
            await this.writeRuntimeFile();
        });
    }
    /**
   * Stops the server and removes it from the list of running servers to clean up on exit.
   */ async stop() {
        if (!this.server) {
            return;
        }
        return new Promise(async (resolve, reject)=>{
            await this.cleanupRuntimeFile();
            this.server.close(async (err)=>{
                if (err) {
                    import_logging.logger.error(`Error shutting down reflection server on port ${this.port}: ${err}`);
                    reject(err);
                }
                const index = ReflectionServer.RUNNING_SERVERS.indexOf(this);
                if (index > -1) {
                    ReflectionServer.RUNNING_SERVERS.splice(index, 1);
                }
                import_logging.logger.debug(`Reflection server on port ${this.port} has successfully shut down.`);
                this.port = null;
                this.server = null;
                resolve();
            });
        });
    }
    /**
   * Writes the runtime file to the project root.
   */ async writeRuntimeFile() {
        try {
            const rootDir = await findProjectRoot();
            const runtimesDir = import_path.default.join(rootDir, ".genkit", "runtimes");
            const date = /* @__PURE__ */ new Date();
            const time = date.getTime();
            const timestamp = date.toISOString();
            const runtimeId = `${process.pid}${this.port !== null ? `-${this.port}` : ""}`;
            this.runtimeFilePath = import_path.default.join(runtimesDir, `${runtimeId}-${time}.json`);
            const fileContent = JSON.stringify({
                id: process.env.GENKIT_RUNTIME_ID || runtimeId,
                pid: process.pid,
                name: this.options.name,
                reflectionServerUrl: `http://localhost:${this.port}`,
                timestamp,
                genkitVersion: `nodejs/${import_index.GENKIT_VERSION}`,
                reflectionApiSpecVersion: import_index.GENKIT_REFLECTION_API_SPEC_VERSION
            }, null, 2);
            await import_promises.default.mkdir(runtimesDir, {
                recursive: true
            });
            await import_promises.default.writeFile(this.runtimeFilePath, fileContent, "utf8");
            import_logging.logger.debug(`Runtime file written: ${this.runtimeFilePath}`);
        } catch (error) {
            import_logging.logger.error(`Error writing runtime file: ${error}`);
        }
    }
    /**
   * Cleans up the port file.
   */ async cleanupRuntimeFile() {
        if (!this.runtimeFilePath) {
            return;
        }
        try {
            const fileContent = await import_promises.default.readFile(this.runtimeFilePath, "utf8");
            const data = JSON.parse(fileContent);
            if (data.pid === process.pid) {
                await import_promises.default.unlink(this.runtimeFilePath);
                import_logging.logger.debug(`Runtime file cleaned up: ${this.runtimeFilePath}`);
            }
        } catch (error) {
            import_logging.logger.error(`Error cleaning up runtime file: ${error}`);
        }
    }
    /**
   * Stops all running reflection servers.
   */ static async stopAll() {
        return Promise.all(ReflectionServer.RUNNING_SERVERS.map((server)=>server.stop()));
    }
}
async function findProjectRoot() {
    let currentDir = process.cwd();
    while(currentDir !== import_path.default.parse(currentDir).root){
        const packageJsonPath = import_path.default.join(currentDir, "package.json");
        try {
            await import_promises.default.access(packageJsonPath);
            return currentDir;
        } catch  {
            currentDir = import_path.default.dirname(currentDir);
        }
    }
    throw new Error("Could not find project root (package.json not found)");
}
if (("TURBOPACK compile-time value", "object") !== "undefined" && "hot" in module) {
    module.hot.accept();
    module.hot.dispose(async ()=>{
        import_logging.logger.debug("Cleaning up reflection server(s) before module reload...");
        await ReflectionServer.stopAll();
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    ReflectionServer,
    RunActionResponseSchema
}); //# sourceMappingURL=reflection.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var telemetryTypes_exports = {};
module.exports = __toCommonJS(telemetryTypes_exports); //# sourceMappingURL=telemetryTypes.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/__codegen/version.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var version_exports = {};
__export(version_exports, {
    version: ()=>version
});
module.exports = __toCommonJS(version_exports);
const version = "1.17.0";
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    version
}); //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var background_action_exports = {};
__export(background_action_exports, {
    OperationSchema: ()=>OperationSchema,
    backgroundAction: ()=>backgroundAction,
    defineBackgroundAction: ()=>defineBackgroundAction,
    isBackgroundAction: ()=>isBackgroundAction,
    lookupBackgroundAction: ()=>lookupBackgroundAction,
    registerBackgroundAction: ()=>registerBackgroundAction
});
module.exports = __toCommonJS(background_action_exports);
var z = __toESM(__turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)"));
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
const OperationSchema = z.object({
    action: z.string().optional(),
    id: z.string(),
    done: z.boolean().optional(),
    output: z.any().optional(),
    error: z.object({
        message: z.string()
    }).passthrough().optional(),
    metadata: z.record(z.string(), z.any()).optional()
});
async function lookupBackgroundAction(registry, key) {
    const root = await registry.lookupAction(key);
    if (!root) return void 0;
    const actionName = key.substring(key.indexOf("/", 1) + 1);
    return new BackgroundActionImpl(root, await registry.lookupAction(`/check-operation/${actionName}/check`), await registry.lookupAction(`/cancel-operation/${actionName}/cancel`));
}
class BackgroundActionImpl {
    __action;
    startAction;
    checkAction;
    cancelAction;
    constructor(startAction, checkAction, cancelAction){
        this.__action = {
            name: startAction.__action.name,
            description: startAction.__action.description,
            inputSchema: startAction.__action.inputSchema,
            inputJsonSchema: startAction.__action.inputJsonSchema,
            metadata: startAction.__action.metadata,
            actionType: startAction.__action.actionType
        };
        this.startAction = startAction;
        this.checkAction = checkAction;
        this.cancelAction = cancelAction;
    }
    async start(input, options) {
        return await this.startAction(input, options);
    }
    async check(operation) {
        return await this.checkAction(operation);
    }
    get supportsCancel() {
        return !!this.cancelAction;
    }
    async cancel(operation) {
        if (!this.cancelAction) {
            return operation;
        }
        return await this.cancelAction(operation);
    }
}
function defineBackgroundAction(registry, config) {
    const act = backgroundAction(config);
    registerBackgroundAction(registry, act);
    return act;
}
function registerBackgroundAction(registry, act, opts) {
    registry.registerAction(act.startAction.__action.actionType, act.startAction, opts);
    registry.registerAction(act.checkAction.__action.actionType, act.checkAction, opts);
    if (act.cancelAction) {
        registry.registerAction(act.cancelAction.__action.actionType, act.cancelAction, opts);
    }
}
function backgroundAction(config) {
    const startAction = (0, import_action.action)({
        actionType: config.actionType,
        name: config.name,
        description: config.description,
        inputSchema: config.inputSchema,
        inputJsonSchema: config.inputJsonSchema,
        outputSchema: OperationSchema,
        metadata: {
            ...config.metadata,
            outputSchema: (0, import_schema.toJsonSchema)({
                schema: config.outputSchema,
                jsonSchema: config.outputJsonSchema
            })
        },
        use: config.use
    }, async (input, options)=>{
        const operation = await config.start(input, options);
        operation.action = `/${config.actionType}/${config.name}`;
        return operation;
    });
    const checkAction = (0, import_action.action)({
        actionType: "check-operation",
        name: `${config.name}/check`,
        description: config.description,
        inputSchema: OperationSchema,
        inputJsonSchema: config.inputJsonSchema,
        outputSchema: OperationSchema,
        metadata: {
            ...config.metadata,
            outputSchema: (0, import_schema.toJsonSchema)({
                schema: config.outputSchema,
                jsonSchema: config.outputJsonSchema
            })
        }
    }, async (input)=>{
        const operation = await config.check(input);
        operation.action = `/${config.actionType}/${config.name}`;
        return operation;
    });
    let cancelAction = void 0;
    if (config.cancel) {
        cancelAction = (0, import_action.action)({
            actionType: "cancel-operation",
            name: `${config.name}/cancel`,
            description: config.description,
            inputSchema: OperationSchema,
            inputJsonSchema: config.inputJsonSchema,
            outputSchema: OperationSchema,
            metadata: {
                ...config.metadata,
                outputSchema: (0, import_schema.toJsonSchema)({
                    schema: config.outputSchema,
                    jsonSchema: config.outputJsonSchema
                })
            }
        }, async (input)=>{
            if (!config.cancel) {
                throw new import_error.GenkitError({
                    status: "UNAVAILABLE",
                    message: `${config.name} does not support cancellation.`
                });
            }
            const operation = await config.cancel(input);
            operation.action = `/${config.actionType}/${config.name}`;
            return operation;
        });
    }
    return new BackgroundActionImpl(startAction, checkAction, cancelAction);
}
function isBackgroundAction(a) {
    return a instanceof BackgroundActionImpl;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    OperationSchema,
    backgroundAction,
    defineBackgroundAction,
    isBackgroundAction,
    lookupBackgroundAction,
    registerBackgroundAction
}); //# sourceMappingURL=background-action.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/flow.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var flow_exports = {};
__export(flow_exports, {
    defineFlow: ()=>defineFlow,
    flow: ()=>flow,
    run: ()=>run
});
module.exports = __toCommonJS(flow_exports);
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)");
function flow(config, fn) {
    const resolvedConfig = typeof config === "string" ? {
        name: config
    } : config;
    return flowAction(resolvedConfig, fn);
}
function defineFlow(registry, config, fn) {
    const f = flow(config, fn);
    registry.registerAction("flow", f);
    return f;
}
function flowAction(config, fn) {
    return (0, import_action.action)({
        actionType: "flow",
        name: config.name,
        inputSchema: config.inputSchema,
        outputSchema: config.outputSchema,
        streamSchema: config.streamSchema,
        metadata: config.metadata
    }, async (input, { sendChunk, context, trace, abortSignal, streamingRequested })=>{
        const ctx = sendChunk;
        ctx.sendChunk = sendChunk;
        ctx.context = context;
        ctx.trace = trace;
        ctx.abortSignal = abortSignal;
        ctx.streamingRequested = streamingRequested;
        return fn(input, ctx);
    });
}
function run(name, funcOrInput, fnOrRegistry, _) {
    let func;
    let input;
    let hasInput = false;
    if (typeof funcOrInput === "function") {
        func = funcOrInput;
    } else {
        input = funcOrInput;
        hasInput = true;
    }
    if (typeof fnOrRegistry === "function") {
        func = fnOrRegistry;
    }
    if (!func) {
        throw new Error("unable to resolve run function");
    }
    return (0, import_tracing.runInNewSpan)({
        metadata: {
            name
        },
        labels: {
            [import_tracing.SPAN_TYPE_ATTR]: "flowStep"
        }
    }, async (meta)=>{
        meta.input = input;
        const output = hasInput ? await func(input) : await func();
        meta.output = JSON.stringify(output);
        return output;
    });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    defineFlow,
    flow,
    run
}); //# sourceMappingURL=flow.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/index.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __reExport = (target, mod, secondTarget)=>(__copyProps(target, mod, "default"), secondTarget && __copyProps(secondTarget, mod, "default"));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var index_exports = {};
__export(index_exports, {
    GENKIT_CLIENT_HEADER: ()=>GENKIT_CLIENT_HEADER,
    GENKIT_REFLECTION_API_SPEC_VERSION: ()=>GENKIT_REFLECTION_API_SPEC_VERSION,
    GENKIT_VERSION: ()=>GENKIT_VERSION,
    GenkitError: ()=>import_error.GenkitError,
    OperationSchema: ()=>import_background_action.OperationSchema,
    UnstableApiError: ()=>import_error.UnstableApiError,
    UserFacingError: ()=>import_error.UserFacingError,
    apiKey: ()=>import_context.apiKey,
    assertUnstable: ()=>import_error.assertUnstable,
    backgroundAction: ()=>import_background_action.backgroundAction,
    defineBackgroundAction: ()=>import_background_action.defineBackgroundAction,
    defineFlow: ()=>import_flow.defineFlow,
    defineJsonSchema: ()=>import_schema.defineJsonSchema,
    defineSchema: ()=>import_schema.defineSchema,
    flow: ()=>import_flow.flow,
    getAsyncContext: ()=>import_async_context.getAsyncContext,
    getCallableJSON: ()=>import_error.getCallableJSON,
    getClientHeader: ()=>getClientHeader,
    getContext: ()=>import_context.getContext,
    getHttpStatus: ()=>import_error.getHttpStatus,
    isBackgroundAction: ()=>import_background_action.isBackgroundAction,
    registerBackgroundAction: ()=>import_background_action.registerBackgroundAction,
    run: ()=>import_flow.run,
    runWithContext: ()=>import_context.runWithContext,
    setClientHeader: ()=>setClientHeader,
    z: ()=>import_zod.z
});
module.exports = __toCommonJS(index_exports);
var import_version = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/__codegen/version.js [app-rsc] (ecmascript)");
var import_zod = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-rsc] (ecmascript)");
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)"), module.exports);
var import_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var import_background_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)");
var import_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)");
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var import_flow = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/flow.js [app-rsc] (ecmascript)");
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)"), module.exports);
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)"), module.exports);
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)"), module.exports);
__reExport(index_exports, __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)"), module.exports);
const GENKIT_VERSION = import_version.version;
const GENKIT_CLIENT_HEADER = `genkit-node/${GENKIT_VERSION} gl-node/${process.versions.node}`;
const GENKIT_REFLECTION_API_SPEC_VERSION = 1;
const clientHeaderGlobalKey = "__genkit_ClientHeader";
function getClientHeader() {
    if (global[clientHeaderGlobalKey]) {
        return GENKIT_CLIENT_HEADER + " " + global[clientHeaderGlobalKey];
    }
    return GENKIT_CLIENT_HEADER;
}
function setClientHeader(header) {
    global[clientHeaderGlobalKey] = header;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    GENKIT_CLIENT_HEADER,
    GENKIT_REFLECTION_API_SPEC_VERSION,
    GENKIT_VERSION,
    GenkitError,
    OperationSchema,
    UnstableApiError,
    UserFacingError,
    apiKey,
    assertUnstable,
    backgroundAction,
    defineBackgroundAction,
    defineFlow,
    defineJsonSchema,
    defineSchema,
    flow,
    getAsyncContext,
    getCallableJSON,
    getClientHeader,
    getContext,
    getHttpStatus,
    isBackgroundAction,
    registerBackgroundAction,
    run,
    runWithContext,
    setClientHeader,
    z,
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)"),
    ...__turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)")
}); //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/registry.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var registry_exports = {};
__export(registry_exports, {
    Registry: ()=>Registry,
    parseRegistryKey: ()=>parseRegistryKey
});
module.exports = __toCommonJS(registry_exports);
var import_dotprompt = __turbopack_context__.r("[project]/node_modules/dotprompt/dist/index.js [app-rsc] (ecmascript)");
var import_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var import_background_action = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)");
var import_error = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var import_schema = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
function parsePluginName(registryKey) {
    const tokens = registryKey.split("/");
    if (tokens.length >= 4) {
        return tokens[2];
    }
    return void 0;
}
function parseRegistryKey(registryKey) {
    const tokens = registryKey.split("/");
    if (tokens.length < 3) {
        return void 0;
    }
    if (tokens.length >= 4) {
        return {
            actionType: tokens[1],
            pluginName: tokens[2],
            actionName: tokens.slice(3).join("/")
        };
    }
    return {
        actionType: tokens[1],
        actionName: tokens[2]
    };
}
class Registry {
    actionsById = {};
    pluginsByName = {};
    schemasByName = {};
    valueByTypeAndName = {};
    allPluginsInitialized = false;
    apiStability = "stable";
    dotprompt;
    parent;
    /** Additional runtime context data for flows and tools. */ context;
    constructor(parent){
        if (parent) {
            this.parent = parent;
            this.apiStability = parent?.apiStability;
            this.dotprompt = parent.dotprompt;
        } else {
            this.dotprompt = new import_dotprompt.Dotprompt({
                schemaResolver: async (name)=>{
                    const resolvedSchema = await this.lookupSchema(name);
                    if (!resolvedSchema) {
                        throw new import_error.GenkitError({
                            message: `Schema '${name}' not found`,
                            status: "NOT_FOUND"
                        });
                    }
                    return (0, import_schema.toJsonSchema)(resolvedSchema);
                }
            });
        }
    }
    /**
   * Creates a new registry overlaid onto the provided registry.
   * @param parent The parent registry.
   * @returns The new overlaid registry.
   */ static withParent(parent) {
        return new Registry(parent);
    }
    /**
   * Looks up an action in the registry.
   * @param key The key of the action to lookup.
   * @returns The action.
   */ async lookupAction(key) {
        const parsedKey = parseRegistryKey(key);
        if (parsedKey?.pluginName && this.pluginsByName[parsedKey.pluginName]) {
            await this.initializePlugin(parsedKey.pluginName);
            if (!this.actionsById[key]) {
                await this.resolvePluginAction(parsedKey.pluginName, parsedKey.actionType, parsedKey.actionName);
            }
        }
        return await this.actionsById[key] || this.parent?.lookupAction(key);
    }
    /**
   * Looks up a background action from the registry.
   * @param key The key of the action to lookup.
   * @returns The action.
   */ async lookupBackgroundAction(key) {
        return (0, import_background_action.lookupBackgroundAction)(this, key);
    }
    /**
   * Registers an action in the registry.
   * @param type The type of the action to register.
   * @param action The action to register.
   */ registerAction(type, action, opts) {
        if (type !== action.__action.actionType) {
            throw new import_error.GenkitError({
                status: "INVALID_ARGUMENT",
                message: `action type (${type}) does not match type on action (${action.__action.actionType})`
            });
        }
        if (opts?.namespace && !action.__action.name.startsWith(`${opts.namespace}/`)) {
            action.__action.name = `${opts.namespace}/${action.__action.name}`;
        }
        const key = `/${type}/${action.__action.name}`;
        import_logging.logger.debug(`registering ${key}`);
        if (this.actionsById.hasOwnProperty(key)) {
            import_logging.logger.warn(`WARNING: ${key} already has an entry in the registry. Overwriting.`);
        }
        this.actionsById[key] = action;
        if (action.__registry) {
            import_logging.logger.error(`ERROR: ${key} already registered.`);
        }
        action.__registry = this;
    }
    /**
   * Registers an action promise in the registry.
   */ registerActionAsync(type, name, action, opts) {
        if (opts?.namespace && !name.startsWith(`${opts.namespace}/`)) {
            name = `${opts.namespace}/${name}`;
        }
        const key = `/${type}/${name}`;
        import_logging.logger.debug(`registering ${key} (async)`);
        if (this.actionsById.hasOwnProperty(key)) {
            import_logging.logger.warn(`WARNING: ${key} already has an entry in the registry. Overwriting.`);
        }
        this.actionsById[key] = action;
    }
    /**
   * Returns all actions that have been registered in the registry.
   * @returns All actions in the registry as a map of <key, action>.
   */ async listActions() {
        await this.initializeAllPlugins();
        const actions = {};
        await Promise.all(Object.entries(this.actionsById).map(async ([key, action])=>{
            actions[key] = await action;
        }));
        return {
            ...await this.parent?.listActions(),
            ...actions
        };
    }
    /**
   * Returns all actions that are resolvable by plugins as well as those that are already
   * in the registry.
   *
   * NOTE: this method should not be used in latency sensitive code paths.
   * It may rely on "admin" API calls such as "list models", which may cause increased cold start latency.
   *
   * @returns All resolvable action metadata as a map of <key, action metadata>.
   */ async listResolvableActions() {
        const resolvableActions = {};
        await Promise.all(Object.entries(this.pluginsByName).map(async ([pluginName, plugin])=>{
            if (plugin.listActions) {
                try {
                    (await plugin.listActions()).forEach((meta)=>{
                        if (!meta.name) {
                            throw new import_error.GenkitError({
                                status: "INVALID_ARGUMENT",
                                message: `Invalid metadata when listing actions from ${pluginName} - name required`
                            });
                        }
                        if (!meta.actionType) {
                            throw new import_error.GenkitError({
                                status: "INVALID_ARGUMENT",
                                message: `Invalid metadata when listing actions from ${pluginName} - actionType required`
                            });
                        }
                        resolvableActions[`/${meta.actionType}/${meta.name}`] = meta;
                    });
                } catch (e) {
                    import_logging.logger.error(`Error listing actions for ${pluginName}
`, e);
                }
            }
        }));
        for (const [key, action] of Object.entries(await this.listActions())){
            resolvableActions[key] = action.__action;
        }
        return {
            ...await this.parent?.listResolvableActions(),
            ...resolvableActions
        };
    }
    /**
   * Initializes all plugins in the registry.
   */ async initializeAllPlugins() {
        if (this.allPluginsInitialized) {
            return;
        }
        for (const pluginName of Object.keys(this.pluginsByName)){
            await this.initializePlugin(pluginName);
        }
        this.allPluginsInitialized = true;
    }
    /**
   * Registers a plugin provider. This plugin must be initialized before it can be used by calling {@link initializePlugin} or {@link initializeAllPlugins}.
   * @param name The name of the plugin to register.
   * @param provider The plugin provider.
   */ registerPluginProvider(name, provider) {
        if (this.pluginsByName[name]) {
            throw new Error(`Plugin ${name} already registered`);
        }
        this.allPluginsInitialized = false;
        let cached;
        let isInitialized = false;
        this.pluginsByName[name] = {
            name: provider.name,
            initializer: ()=>{
                if (!isInitialized) {
                    cached = provider.initializer();
                    isInitialized = true;
                }
                return cached;
            },
            resolver: async (actionType, actionName)=>{
                if (provider.resolver) {
                    await provider.resolver(actionType, actionName);
                }
            },
            listActions: async ()=>{
                if (provider.listActions) {
                    return await provider.listActions();
                }
                return [];
            }
        };
    }
    /**
   * Looks up a plugin.
   * @param name The name of the plugin to lookup.
   * @returns The plugin provider.
   */ lookupPlugin(name) {
        return this.pluginsByName[name] || this.parent?.lookupPlugin(name);
    }
    /**
   * Resolves a new Action dynamically by registering it.
   * @param pluginName The name of the plugin
   * @param actionType The type of the action
   * @param actionName The name of the action
   * @returns
   */ async resolvePluginAction(pluginName, actionType, actionName) {
        const plugin = this.pluginsByName[pluginName];
        if (plugin) {
            return await (0, import_action.runOutsideActionRuntimeContext)(async ()=>{
                if (plugin.resolver) {
                    await plugin.resolver(actionType, actionName);
                }
            });
        }
    }
    /**
   * Initializes a plugin already registered with {@link registerPluginProvider}.
   * @param name The name of the plugin to initialize.
   * @returns The plugin.
   */ async initializePlugin(name) {
        if (this.pluginsByName[name]) {
            return await (0, import_action.runOutsideActionRuntimeContext)(()=>this.pluginsByName[name].initializer());
        }
    }
    /**
   * Registers a schema.
   * @param name The name of the schema to register.
   * @param data The schema to register (either a Zod schema or a JSON schema).
   */ registerSchema(name, data) {
        if (this.schemasByName[name]) {
            throw new Error(`Schema ${name} already registered`);
        }
        this.schemasByName[name] = data;
    }
    registerValue(type, name, value) {
        if (!this.valueByTypeAndName[type]) {
            this.valueByTypeAndName[type] = {};
        }
        this.valueByTypeAndName[type][name] = value;
    }
    async lookupValue(type, key) {
        const pluginName = parsePluginName(key);
        if (!this.valueByTypeAndName[type]?.[key] && pluginName) {
            await this.initializePlugin(pluginName);
        }
        return this.valueByTypeAndName[type]?.[key] || this.parent?.lookupValue(type, key);
    }
    async listValues(type) {
        await this.initializeAllPlugins();
        return {
            ...await this.parent?.listValues(type) || {},
            ...this.valueByTypeAndName[type] || {}
        };
    }
    /**
   * Looks up a schema.
   * @param name The name of the schema to lookup.
   * @returns The schema.
   */ lookupSchema(name) {
        return this.schemasByName[name] || this.parent?.lookupSchema(name);
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    Registry,
    parseRegistryKey
}); //# sourceMappingURL=registry.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/node-async-context.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var node_async_context_exports = {};
__export(node_async_context_exports, {
    NodeAsyncContext: ()=>NodeAsyncContext,
    initNodeAsyncContext: ()=>initNodeAsyncContext
});
module.exports = __toCommonJS(node_async_context_exports);
var import_node_async_hooks = __turbopack_context__.r("[externals]/node:async_hooks [external] (node:async_hooks, cjs)");
var import_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
class NodeAsyncContext {
    asls = {};
    getStore(key) {
        return this.asls[key]?.getStore();
    }
    run(key, store, callback) {
        if (!this.asls[key]) {
            this.asls[key] = new import_node_async_hooks.AsyncLocalStorage();
        }
        return this.asls[key].run(store, callback);
    }
}
function initNodeAsyncContext() {
    (0, import_async_context.setAsyncContext)(new NodeAsyncContext());
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    NodeAsyncContext,
    initNodeAsyncContext
}); //# sourceMappingURL=node-async-context.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing/node-telemetry-provider.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var node_telemetry_provider_exports = {};
__export(node_telemetry_provider_exports, {
    initNodeTelemetryProvider: ()=>initNodeTelemetryProvider
});
module.exports = __toCommonJS(node_telemetry_provider_exports);
var import_sdk_node = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-node/build/src/index.js [app-rsc] (ecmascript)");
var import_sdk_trace_base = __turbopack_context__.r("[project]/node_modules/@opentelemetry/sdk-trace-base/build/esm/index.js [app-rsc] (ecmascript)");
var import_logging = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var import_tracing = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing.js [app-rsc] (ecmascript)");
var import_utils = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)");
var import_exporter = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)");
let telemetrySDK = null;
let nodeOtelConfig = null;
function initNodeTelemetryProvider() {
    (0, import_tracing.setTelemetryProvider)({
        enableTelemetry,
        flushTracing
    });
}
async function enableTelemetry(telemetryConfig) {
    if (process.env.GENKIT_TELEMETRY_SERVER) {
        (0, import_exporter.setTelemetryServerUrl)(process.env.GENKIT_TELEMETRY_SERVER);
    }
    telemetryConfig = telemetryConfig instanceof Promise ? await telemetryConfig : telemetryConfig;
    nodeOtelConfig = telemetryConfig || {};
    const processors = [
        createTelemetryServerProcessor()
    ];
    if (nodeOtelConfig.traceExporter) {
        throw new Error("Please specify spanProcessors instead.");
    }
    if (nodeOtelConfig.spanProcessors) {
        processors.push(...nodeOtelConfig.spanProcessors);
    }
    if (nodeOtelConfig.spanProcessor) {
        processors.push(nodeOtelConfig.spanProcessor);
        delete nodeOtelConfig.spanProcessor;
    }
    nodeOtelConfig.spanProcessors = processors;
    telemetrySDK = new import_sdk_node.NodeSDK(nodeOtelConfig);
    telemetrySDK.start();
    process.on("SIGTERM", async ()=>await cleanUpTracing());
}
async function cleanUpTracing() {
    if (!telemetrySDK) {
        return;
    }
    await maybeFlushMetrics();
    await telemetrySDK.shutdown();
    import_logging.logger.debug("OpenTelemetry SDK shut down.");
    telemetrySDK = null;
}
function createTelemetryServerProcessor() {
    const exporter = new import_exporter.TraceServerExporter();
    return (0, import_utils.isDevEnv)() ? new import_sdk_trace_base.SimpleSpanProcessor(exporter) : new import_sdk_trace_base.BatchSpanProcessor(exporter);
}
function maybeFlushMetrics() {
    if (nodeOtelConfig?.metricReader) {
        return nodeOtelConfig.metricReader.forceFlush();
    }
    return Promise.resolve();
}
async function flushTracing() {
    if (nodeOtelConfig?.spanProcessors) {
        await Promise.all(nodeOtelConfig.spanProcessors.map((p)=>p.forceFlush()));
    }
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    initNodeTelemetryProvider
}); //# sourceMappingURL=node-telemetry-provider.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/node.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);
var node_exports = {};
__export(node_exports, {
    initNodeFeatures: ()=>initNodeFeatures
});
module.exports = __toCommonJS(node_exports);
var import_node_async_context = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/node-async-context.js [app-rsc] (ecmascript)");
var import_node_telemetry_provider = __turbopack_context__.r("[project]/node_modules/@genkit-ai/core/lib/tracing/node-telemetry-provider.js [app-rsc] (ecmascript)");
function initNodeFeatures() {
    (0, import_node_async_context.initNodeAsyncContext)();
    (0, import_node_telemetry_provider.initNodeTelemetryProvider)();
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
    initNodeFeatures
}); //# sourceMappingURL=node.js.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/logging.mjs [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "logger": (()=>logger)
});
const LOG_LEVELS = [
    "debug",
    "info",
    "warn",
    "error"
];
const loggerKey = "__genkit_logger";
const _defaultLogger = {
    shouldLog (targetLevel) {
        return LOG_LEVELS.indexOf(this.level) <= LOG_LEVELS.indexOf(targetLevel);
    },
    debug (...args) {
        this.shouldLog("debug") && console.debug(...args);
    },
    info (...args) {
        this.shouldLog("info") && console.info(...args);
    },
    warn (...args) {
        this.shouldLog("warn") && console.warn(...args);
    },
    error (...args) {
        this.shouldLog("error") && console.error(...args);
    },
    level: "info"
};
function getLogger() {
    if (!global[loggerKey]) {
        global[loggerKey] = _defaultLogger;
    }
    return global[loggerKey];
}
class Logger {
    defaultLogger = _defaultLogger;
    init(fn) {
        global[loggerKey] = fn;
    }
    info(...args) {
        getLogger().info.apply(getLogger(), args);
    }
    debug(...args) {
        getLogger().debug.apply(getLogger(), args);
    }
    error(...args) {
        getLogger().error.apply(getLogger(), args);
    }
    warn(...args) {
        getLogger().warn.apply(getLogger(), args);
    }
    setLogLevel(level) {
        getLogger().level = level;
    }
    logStructured(msg, metadata) {
        getLogger().info(msg, metadata);
    }
    logStructuredError(msg, metadata) {
        getLogger().error(msg, metadata);
    }
}
const logger = new Logger();
;
 //# sourceMappingURL=logging.mjs.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GENKIT_CLIENT_HEADER": (()=>GENKIT_CLIENT_HEADER),
    "GENKIT_REFLECTION_API_SPEC_VERSION": (()=>GENKIT_REFLECTION_API_SPEC_VERSION),
    "GENKIT_VERSION": (()=>GENKIT_VERSION),
    "getClientHeader": (()=>getClientHeader),
    "setClientHeader": (()=>setClientHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$_$5f$codegen$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/__codegen/version.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$async$2d$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/flow.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$plugin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$reflection$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$telemetryTypes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)");
;
const GENKIT_VERSION = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$_$5f$codegen$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["version"];
const GENKIT_CLIENT_HEADER = `genkit-node/${GENKIT_VERSION} gl-node/${process.versions.node}`;
const GENKIT_REFLECTION_API_SPEC_VERSION = 1;
;
;
;
;
;
;
;
;
;
;
;
;
const clientHeaderGlobalKey = "__genkit_ClientHeader";
function getClientHeader() {
    if (global[clientHeaderGlobalKey]) {
        return GENKIT_CLIENT_HEADER + " " + global[clientHeaderGlobalKey];
    }
    return GENKIT_CLIENT_HEADER;
}
function setClientHeader(header) {
    global[clientHeaderGlobalKey] = header;
}
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$_$5f$codegen$2f$version$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/__codegen/version.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$async$2d$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/flow.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$plugin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$reflection$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$telemetryTypes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ValidationError": (()=>ValidationError),
    "defineJsonSchema": (()=>defineJsonSchema),
    "defineSchema": (()=>defineSchema),
    "parseSchema": (()=>parseSchema),
    "toJsonSchema": (()=>toJsonSchema),
    "validateSchema": (()=>validateSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2f$dist$2f$ajv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ajv/dist/ajv.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2d$formats$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ajv-formats/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod-to-json-schema/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zod-to-json-schema/dist/esm/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
;
;
;
;
;
const ajv = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2f$dist$2f$ajv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]();
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2d$formats$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])(ajv);
const jsonSchemas = /* @__PURE__ */ new WeakMap();
const validators = /* @__PURE__ */ new WeakMap();
class ValidationError extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenkitError"] {
    constructor({ data, errors, schema }){
        super({
            status: "INVALID_ARGUMENT",
            message: `Schema validation failed. Parse Errors:

${errors.map((e)=>`- ${e.path}: ${e.message}`).join("\n")}

Provided data:

${JSON.stringify(data, null, 2)}

Required JSON schema:

${JSON.stringify(schema, null, 2)}`,
            detail: {
                errors,
                schema
            }
        });
    }
}
function toJsonSchema({ jsonSchema, schema }) {
    if (!jsonSchema && !schema) return null;
    if (jsonSchema) return jsonSchema;
    if (jsonSchemas.has(schema)) return jsonSchemas.get(schema);
    const outSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])(schema, {
        $refStrategy: "none",
        removeAdditionalStrategy: "strict"
    });
    jsonSchemas.set(schema, outSchema);
    return outSchema;
}
function toErrorDetail(error) {
    return {
        path: error.instancePath.substring(1).replace(/\//g, ".") || "(root)",
        message: error.message
    };
}
function validateSchema(data, options) {
    const toValidate = toJsonSchema(options);
    if (!toValidate) {
        return {
            valid: true,
            schema: toValidate
        };
    }
    const validator = validators.get(toValidate) || ajv.compile(toValidate);
    const valid = validator(data);
    const errors = validator.errors?.map((e)=>e);
    return {
        valid,
        errors: errors?.map(toErrorDetail),
        schema: toValidate
    };
}
function parseSchema(data, options) {
    const { valid, errors, schema } = validateSchema(data, options);
    if (!valid) throw new ValidationError({
        data,
        errors,
        schema
    });
    return data;
}
function defineSchema(registry, name, schema) {
    registry.registerSchema(name, {
        schema
    });
    return schema;
}
function defineJsonSchema(registry, name, jsonSchema) {
    registry.registerSchema(name, {
        jsonSchema
    });
    return jsonSchema;
}
;
 //# sourceMappingURL=schema.mjs.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2f$dist$2f$ajv$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ajv/dist/ajv.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$ajv$2d$formats$2f$dist$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/ajv-formats/dist/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2d$to$2d$json$2d$schema$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod-to-json-schema/dist/esm/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.mjs [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GENKIT_CLIENT_HEADER": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["GENKIT_CLIENT_HEADER"]),
    "GENKIT_REFLECTION_API_SPEC_VERSION": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["GENKIT_REFLECTION_API_SPEC_VERSION"]),
    "GENKIT_VERSION": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["GENKIT_VERSION"]),
    "GenkitError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenkitError"]),
    "OperationSchema": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["OperationSchema"]),
    "UnstableApiError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UnstableApiError"]),
    "UserFacingError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["UserFacingError"]),
    "apiKey": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiKey"]),
    "assertUnstable": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["assertUnstable"]),
    "backgroundAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["backgroundAction"]),
    "defineBackgroundAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defineBackgroundAction"]),
    "defineFlow": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defineFlow"]),
    "defineJsonSchema": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defineJsonSchema"]),
    "defineSchema": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["defineSchema"]),
    "flow": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["flow"]),
    "getAsyncContext": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$async$2d$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAsyncContext"]),
    "getCallableJSON": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCallableJSON"]),
    "getClientHeader": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getClientHeader"]),
    "getContext": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getContext"]),
    "getHttpStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getHttpStatus"]),
    "isBackgroundAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isBackgroundAction"]),
    "registerBackgroundAction": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerBackgroundAction"]),
    "run": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["run"]),
    "runWithContext": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["runWithContext"]),
    "setClientHeader": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setClientHeader"]),
    "z": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["z"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$async$2d$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/async-context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$background$2d$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/background-action.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$context$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/context.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$flow$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/flow.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$plugin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/plugin.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$reflection$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/reflection.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$schema$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/schema.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$telemetryTypes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/telemetryTypes.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/utils.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/index.mjs [app-rsc] (ecmascript) <locals>");
__turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$action$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$plugin$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$reflection$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$telemetryTypes$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$utils$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__);
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing.mjs [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "enableTelemetry": (()=>enableTelemetry),
    "ensureBasicTelemetryInstrumentation": (()=>ensureBasicTelemetryInstrumentation),
    "flushTracing": (()=>flushTracing),
    "setTelemetryProvider": (()=>setTelemetryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$exporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)");
;
;
;
;
;
const instrumentationKey = "__GENKIT_TELEMETRY_INSTRUMENTED";
const telemetryProviderKey = "__GENKIT_TELEMETRY_PROVIDER";
async function ensureBasicTelemetryInstrumentation() {
    await checkFirebaseMonitoringAutoInit();
    if (global[instrumentationKey]) {
        return await global[instrumentationKey];
    }
    await enableTelemetry({});
}
async function checkFirebaseMonitoringAutoInit() {
    if (!global[instrumentationKey] && process.env.ENABLE_FIREBASE_MONITORING === "true") {
        try {
            const importModule = new Function("moduleName", "return import(moduleName)");
            const firebaseModule = await importModule("@genkit-ai/firebase");
            firebaseModule.enableFirebaseTelemetry();
        } catch (e) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logger"].warn("It looks like you're trying to enable firebase monitoring, but haven't installed the firebase plugin. Please run `npm i --save @genkit-ai/firebase` and redeploy.");
        }
    }
}
function getTelemetryProvider() {
    if (global[telemetryProviderKey]) {
        return global[telemetryProviderKey];
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["GenkitError"]({
        status: "FAILED_PRECONDITION",
        message: "TelemetryProvider is not initialized."
    });
}
function setTelemetryProvider(provider) {
    if (global[telemetryProviderKey]) return;
    global[telemetryProviderKey] = provider;
}
async function enableTelemetry(telemetryConfig) {
    global[instrumentationKey] = telemetryConfig instanceof Promise ? telemetryConfig : Promise.resolve();
    return getTelemetryProvider().enableTelemetry(telemetryConfig);
}
async function flushTracing() {
    return getTelemetryProvider().flushTracing();
}
;
 //# sourceMappingURL=tracing.mjs.map
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing.mjs [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$error$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/error.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$logging$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/logging.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$exporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing.mjs [app-rsc] (ecmascript) <locals>");
}}),
"[project]/node_modules/@genkit-ai/core/lib/tracing.mjs [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "enableTelemetry": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["enableTelemetry"]),
    "ensureBasicTelemetryInstrumentation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ensureBasicTelemetryInstrumentation"]),
    "flushTracing": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushTracing"]),
    "setTelemetryProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setTelemetryProvider"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$exporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/exporter.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/instrumentation.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing/types.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@genkit-ai/core/lib/tracing.mjs [app-rsc] (ecmascript) <locals>");
__turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$exporter$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$instrumentation$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__), __turbopack_context__.j(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$genkit$2d$ai$2f$core$2f$lib$2f$tracing$2f$types$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__);
}}),

};

//# sourceMappingURL=node_modules_%40genkit-ai_core_lib_8f01467c._.js.map