{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/components/admin/AdminSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { FileText, PlusCircle } from \"lucide-react\";\r\n\r\nconst menuItems = [\r\n  { href: \"/admin/posts\", label: \"Manage Posts\", icon: FileText },\r\n  { href: \"/admin/posts/create\", label: \"Create Post\", icon: PlusCircle },\r\n];\r\n\r\nexport function AdminSidebar() {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <aside className=\"w-64 bg-background border-r p-4 hidden md:block\">\r\n      <nav className=\"space-y-2\">\r\n        {menuItems.map((item) => (\r\n          <Button\r\n            key={item.href}\r\n            variant={pathname === item.href ? \"secondary\" : \"ghost\"}\r\n            className=\"w-full justify-start\"\r\n            asChild\r\n          >\r\n            <Link href={item.href}>\r\n              <item.icon className=\"mr-2 h-4 w-4\" />\r\n              {item.label}\r\n            </Link>\r\n          </Button>\r\n        ))}\r\n      </nav>\r\n    </aside>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAAA;AANA;;;;;;AAQA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAgB,OAAO;QAAgB,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9D;QAAE,MAAM;QAAuB,OAAO;QAAe,MAAM,kNAAA,CAAA,aAAU;IAAC;CACvE;AAEM,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC;QAAM,WAAU;kBACf,cAAA,8OAAC;YAAI,WAAU;sBACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,kIAAA,CAAA,SAAM;oBAEL,SAAS,aAAa,KAAK,IAAI,GAAG,cAAc;oBAChD,WAAU;oBACV,OAAO;8BAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,KAAK,IAAI;;0CACnB,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,KAAK;;;;;;;mBAPR,KAAK,IAAI;;;;;;;;;;;;;;;AAc1B", "debugId": null}}]}